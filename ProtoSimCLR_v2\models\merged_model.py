import torch.nn as nn
import torch
import torch.nn as nn
import torch.nn.functional as F
class CrossAttentionFusion(nn.Module):
    def __init__(self, seq_dim, img_dim, fusion_dim=256, num_heads=8, dropout=0.1):
        """
        全局特征交叉注意力融合模块
        :param seq_dim: 序列分支的特征维度
        :param img_dim: 图像分支的特征维度
        :param fusion_dim: 融合特征的维度
        :param num_heads: 注意力头数
        :param dropout: dropout比率
        """
        super(CrossAttentionFusion, self).__init__()

        # 特征投影层
        self.seq_projection = nn.Sequential(
            nn.Linear(seq_dim, fusion_dim),
            nn.GELU(),
            nn.Dropout(dropout)
        )

        self.img_projection = nn.Sequential(
            nn.Linear(img_dim, fusion_dim),
            nn.GELU(),
            nn.Dropout(dropout)
        )

        # 交叉注意力层 (序列特征作为query，图像特征作为key/value)
        self.seq_to_img_attn = nn.MultiheadAttention(
            embed_dim=fusion_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )

        # 交叉注意力层 (图像特征作为query，序列特征作为key/value)
        self.img_to_seq_attn = nn.MultiheadAttention(
            embed_dim=fusion_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )

        # 门控融合层
        self.gate = nn.Sequential(
            nn.Linear(2 * fusion_dim, fusion_dim),
            nn.Sigmoid()
        )

        # 融合后处理
        self.fusion_norm = nn.LayerNorm(fusion_dim)
        self.fusion_ffn = nn.Sequential(
            nn.Linear(fusion_dim, fusion_dim * 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(fusion_dim * 2, fusion_dim)
        )

        # 初始化权重
        self._init_weights()

    def _init_weights(self):
        """初始化注意力参数"""
        for module in [self.seq_to_img_attn, self.img_to_seq_attn]:
            nn.init.xavier_uniform_(module.in_proj_weight)
            nn.init.constant_(module.in_proj_bias, 0.)
            nn.init.xavier_uniform_(module.out_proj.weight)
            nn.init.constant_(module.out_proj.bias, 0.)

        for layer in [self.seq_projection, self.img_projection, self.fusion_ffn]:
            for module in layer:
                if isinstance(module, nn.Linear):
                    nn.init.xavier_uniform_(module.weight)
                    if module.bias is not None:
                        nn.init.constant_(module.bias, 0.)

    def forward(self, seq_features, img_features):
        """
        前向传播
        :param seq_features: 序列分支特征 [B, seq_dim]
        :param img_features: 图像分支特征 [B, img_dim]
        :return: 融合后的特征 [B, fusion_dim]
        """
        # 投影特征到相同维度
        proj_seq = self.seq_projection(seq_features)  # [B, fusion_dim]
        proj_img = self.img_projection(img_features)  # [B, fusion_dim]

        # 添加序列维度用于注意力计算
        proj_seq = proj_seq.unsqueeze(1)  # [B, 1, fusion_dim]
        proj_img = proj_img.unsqueeze(1)  # [B, 1, fusion_dim]

        # 交叉注意力1: 序列特征作为query，图像特征作为key和value
        seq_attended, _ = self.seq_to_img_attn(
            query=proj_seq,
            key=proj_img,
            value=proj_img
        )

        # 交叉注意力2: 图像特征作为query，序列特征作为key和value
        img_attended, _ = self.img_to_seq_attn(
            query=proj_img,
            key=proj_seq,
            value=proj_seq
        )

        # 移除序列维度
        seq_attended = seq_attended.squeeze(1)  # [B, fusion_dim]
        img_attended = img_attended.squeeze(1)  # [B, fusion_dim]

        # 门控融合
        combined = torch.cat([seq_attended, img_attended], dim=1)  # [B, 2*fusion_dim]
        gate_value = self.gate(combined)  # [B, fusion_dim]

        # 加权融合
        fused_features = gate_value * seq_attended + (1 - gate_value) * img_attended

        # 后处理
        fused_features = self.fusion_norm(fused_features)
        fused_features = fused_features + self.fusion_ffn(fused_features)

        return fused_features


class MergedModel(nn.Module):
    def __init__(self, **kargs):
        """
        双分支特征融合模型
        :param kargs: 包含以下参数
          - seq_branch_encoder: 序列分支的编码器
          - img_branch_encoder: 图像分支的编码器
          - seq_dim: 序列分支的输出维度
          - img_dim: 图像分支的输出维度
          - fusion_dim: 融合特征的维度 (默认:256)
          - num_classes: 输出类别数
        """
        super(MergedModel, self).__init__()
        self.seq_branch_encoder = kargs.get('seq_branch_encoder',None)
        self.img_branch_encoder = kargs.get('img_branch_encoder',None)
        if self.seq_branch_encoder is None:
            raise ValueError("请提供序列分支的编码器")
        if self.img_branch_encoder is None:
            raise ValueError("请提供图像分支的编码器")

        # 获取特征维度
        seq_dim = kargs.get('seq_dim', 128)
        img_dim = kargs.get('img_dim', 128)
        fusion_dim = kargs.get('fusion_dim', 256)
        num_classes = kargs.get('num_classes', None)
        if num_classes is None:
            raise ValueError("请提供输出类别数")

        # 特征融合模块
        self.feature_fusion = CrossAttentionFusion(
            seq_dim=seq_dim,
            img_dim=img_dim,
            fusion_dim=fusion_dim
        )
        self.seq_mlp = nn.Sequential(nn.Linear(seq_dim,seq_dim), nn.ReLU(), nn.Linear(seq_dim,fusion_dim//2))
        self.img_mlp = nn.Sequential(nn.Linear(img_dim,img_dim), nn.ReLU(), nn.Linear(img_dim,fusion_dim//2))
        # 分类器
        self.out_mlp = nn.Sequential(nn.Linear(fusion_dim,fusion_dim), nn.ReLU(), nn.Linear(fusion_dim,fusion_dim))

        # 加载预训练权重（如果提供了路径）
        self._load_pretrained_weights(kargs)

    def _load_pretrained_weights(self, kargs):
        """
        加载预训练权重
        """
        # 加载序列分支预训练权重
        seq_pretrained_path = kargs.get('seq_pretrained_path', None)
        if seq_pretrained_path is not None:
            try:
                checkpoint = torch.load(seq_pretrained_path, map_location='cpu')
                # 如果保存的是整个模型状态，使用 'state_dict' 键
                if 'state_dict' in checkpoint:
                    state_dict = checkpoint['state_dict']
                    state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
                    self.seq_branch_encoder.load_state_dict(state_dict)
                else:
                    # 如果直接保存了模型状态字典
                    self.seq_branch_encoder.load_state_dict(checkpoint)
                print(f"成功加载序列分支预训练权重: {seq_pretrained_path}")
            except Exception as e:
                print(f"加载序列分支预训练权重失败: {e}")

        # 加载图像分支预训练权重
        img_pretrained_path = kargs.get('img_pretrained_path', None)
        if img_pretrained_path is not None:
            try:
                checkpoint = torch.load(img_pretrained_path, map_location='cpu')
                # 如果保存的是整个模型状态，使用 'state_dict' 键
                if 'state_dict' in checkpoint:
                    state_dict = checkpoint['state_dict']
                    state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
                    self.img_branch_encoder.load_state_dict(state_dict)
                else:
                    # 如果直接保存了模型状态字典
                    self.img_branch_encoder.load_state_dict(checkpoint)
                print(f"成功加载图像分支预训练权重: {img_pretrained_path}")
            except Exception as e:
                print(f"加载图像分支预训练权重失败: {e}")

    def freeze_backbone(self):
        """冻结编码器参数，只训练分类器"""
        for param in self.seq_branch_encoder.parameters():
            param.requires_grad = False
        for param in self.img_branch_encoder.parameters():
            param.requires_grad = False
        for param in self.feature_fusion.parameters():
            param.requires_grad = True
        for param in self.classifier.parameters():
            param.requires_grad = True
        print("Backbone frozen, only classifier trainable")

    def unfreeze_backbone(self):
        """解冻所有参数"""
        for param in self.parameters():
            param.requires_grad = True
        print("All parameters unfrozen")

    def forward(self, img_branch_input, seq_branch_input, attention_mask=None):
        """
        前向传播
        :param img_branch_input: 图像输入 [B, C, H, W]
        :param seq_branch_input: 序列输入 [B, seq_len]
        :param attention_mask: 注意力掩码 [B, seq_len] (可选)
        :return: 分类结果 [B, num_classes]
        """
        # 获取特征
        seq_features = self.seq_branch_encoder(seq_branch_input, attention_mask)  # [B, seq_dim]
        img_features = self.img_branch_encoder(img_branch_input)  # [B, img_dim]
        seq_out = self.seq_mlp(seq_features)
        img_out = self.img_mlp(img_features)
        # 特征融合
        fused_features = self.feature_fusion(seq_features, img_features)  # [B, fusion_dim]

        # 分类
        output = self.out_mlp(fused_features)  # [B, fusion_dim]

        return dict(seq_embedding=seq_out, img_embedding=img_out, out_embedding=output)


if __name__ == '__main__':
    from resnet_simclr import ResNetSimCLR
    from bert_encoder import BertEncoder, BertEncoderConfig

    # 创建模型实例
    sq_cfg = BertEncoderConfig(
        input_dim=1024,
        conv_output_channels=256,
        out_dim=128,
        num_hidden_layers=6
    )
    seq_branch_encoder = BertEncoder(sq_cfg)
    img_branch_encoder = ResNetSimCLR(base_model='resnet18', out_dim=128)

    model = MergedModel(
        seq_branch_encoder=seq_branch_encoder,
        img_branch_encoder=img_branch_encoder,
        seq_dim=128,
        img_dim=128,
        fusion_dim=256,
        num_classes=10
    )

    print(f"Model created successfully!")
    print(f"Total parameters: {sum(p.numel() for p in model.parameters()):,}")
    print(f"Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # 验证输入输出
    try:
        dummy_img = torch.randn(2, 3, 32, 32)
        dummy_seq = torch.randn(2, 1024)
        dummy_mask = torch.ones(2, 64)  # 1024/16=64

        output = model(dummy_img, dummy_seq, dummy_mask)
        print(f"模型测试通过，输出形状: {output.shape}")
        print(f"输出类型: {type(output)}")

    except Exception as e:
        print(f"模型前向传播错误: {str(e)}")
        import traceback
        traceback.print_exc()


