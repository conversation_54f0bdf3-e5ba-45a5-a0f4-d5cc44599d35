import torch.nn as nn
import torchvision.models as models

class ResNetSimCLR(nn.Module):
    def __init__(self, base_model, out_dim, num_classes):
        super(ResNetSimCLR, self).__init__()
        self.resnet_dict = {
            "resnet18": models.resnet18(pretrained=False, num_classes=out_dim),
            "resnet50": models.resnet50(pretrained=False, num_classes=out_dim)
        }
        self.backbone = self._get_basemodel(base_model)
        fc_in_features = self.backbone.fc.in_features
        self.backbone.fc = nn.Identity ()
        self.classifier = nn.Linear(fc_in_features, num_classes)
    def _get_basemodel(self, model_name):
        model = self.resnet_dict.get(model_name)
        if model is None:
            raise ValueError(f"Invalid backbone: {model_name}. Choose from {list(self.resnet_dict.keys())}")
        return model
    def freeze_backbone(self):
        for param in self.backbone.parameters():
            param.requires_grad = False
            # 解冻所有BN层
        for module in self.backbone.modules():
            if isinstance(module, (nn.BatchNorm1d, nn.BatchNorm2d)):
                for param in module.parameters():
                    param.requires_grad = True
                    print(f"freeze{module}")
        for param in self.classifier.parameters():
            param.requires_grad = True
        print("Backbone frozen")

    def unfreeze_backbone(self):
        for param in self.backbone.parameters():
            param.requires_grad = True
        for param in self.classifier.parameters():
            param.requires_grad = True
        print("Backbone unfrozen")

    def forward(self, x):
        x = self.backbone(x)
        x = self.classifier(x)
        return x