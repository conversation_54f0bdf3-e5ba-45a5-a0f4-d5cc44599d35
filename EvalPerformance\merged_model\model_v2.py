import torch
import torch.nn as nn
import torch.nn.functional as F


class EnhancedCrossAttentionFusion(nn.Module):
    def __init__(self, seq_dim, img_dim, fusion_dim=256, num_heads=8, dropout=0.1):
        """
        增强的全局特征交叉注意力融合模块
        :param seq_dim: 序列分支的特征维度
        :param img_dim: 图像分支的特征维度
        :param fusion_dim: 融合特征的维度
        :param num_heads: 注意力头数
        :param dropout: dropout比率
        """
        super().__init__()

        # 增强的特征投影层（深度投影）
        self.seq_projection = nn.Sequential(
            nn.Linear(seq_dim, fusion_dim * 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(fusion_dim * 2, fusion_dim)  # 深度投影层
        )

        self.img_projection = nn.Sequential(
            nn.Linear(img_dim, fusion_dim * 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(fusion_dim * 2, fusion_dim)  # 深度投影层
        )

        # 预层归一化 (Pre-LN)
        self.seq_norm = nn.LayerNorm(fusion_dim)
        self.img_norm = nn.LayerNorm(fusion_dim)

        # 交叉注意力层
        self.seq_to_img_attn = nn.MultiheadAttention(
            embed_dim=fusion_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )

        self.img_to_seq_attn = nn.MultiheadAttention(
            embed_dim=fusion_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )

        # 自适应门控融合
        self.gate = nn.Sequential(
            nn.Linear(2 * fusion_dim, fusion_dim),
            nn.ReLU(),
            nn.Linear(fusion_dim, 1),  # 输出单个门控值
            nn.Sigmoid()
        )

        # 融合后处理
        self.fusion_norm = nn.LayerNorm(fusion_dim)
        self.fusion_ffn = nn.Sequential(
            nn.Linear(fusion_dim, fusion_dim * 4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(fusion_dim * 4, fusion_dim)
        )

        self.dropout = nn.Dropout(dropout)
        self._init_weights()

    def _init_weights(self):
        """使用Kaiming初始化提升训练稳定性"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

        # 注意力层特定初始化
        for module in [self.seq_to_img_attn, self.img_to_seq_attn]:
            nn.init.xavier_uniform_(module.in_proj_weight)
            nn.init.constant_(module.in_proj_bias, 0.)
            nn.init.xavier_uniform_(module.out_proj.weight)
            nn.init.constant_(module.out_proj.bias, 0.)

    def forward(self, seq_features, img_features):
        """
        前向传播
        :param seq_features: 序列分支特征 [B, seq_dim]
        :param img_features: 图像分支特征 [B, img_dim]
        :return: 融合后的特征 [B, fusion_dim]
        """
        # 投影特征到融合维度
        proj_seq = self.seq_projection(seq_features)  # [B, fusion_dim]
        proj_img = self.img_projection(img_features)  # [B, fusion_dim]

        # 预层归一化
        proj_seq = self.seq_norm(proj_seq)
        proj_img = self.img_norm(proj_img)

        # 添加序列维度用于注意力计算
        proj_seq = proj_seq.unsqueeze(1)  # [B, 1, fusion_dim]
        proj_img = proj_img.unsqueeze(1)  # [B, 1, fusion_dim]

        # 交叉注意力1: 序列特征作为query，图像特征作为key和value
        seq_attended, _ = self.seq_to_img_attn(
            query=proj_seq,
            key=proj_img,
            value=proj_img
        )

        # 交叉注意力2: 图像特征作为query，序列特征作为key和value
        img_attended, _ = self.img_to_seq_attn(
            query=proj_img,
            key=proj_seq,
            value=proj_seq
        )

        # 移除序列维度
        seq_attended = seq_attended.squeeze(1)  # [B, fusion_dim]
        img_attended = img_attended.squeeze(1)  # [B, fusion_dim]

        # 门控融合
        combined = torch.cat([seq_attended, img_attended], dim=1)  # [B, 2*fusion_dim]
        gate_value = self.gate(combined)  # [B, 1]

        # 加权融合
        fused_features = gate_value * seq_attended + (1 - gate_value) * img_attended

        # 后处理 (残差连接+FFN)
        fused_features = self.fusion_norm(fused_features)
        fused_features = fused_features + self.fusion_ffn(fused_features)

        return fused_features


class MergedModel(nn.Module):
    def __init__(self, **kwargs):
        """
        增强的双分支特征融合模型
        :param kwargs: 包含以下参数
          - seq_branch_encoder: 序列分支的编码器
          - img_branch_encoder: 图像分支的编码器
          - seq_dim: 序列分支的输出维度
          - img_dim: 图像分支的输出维度
          - fusion_dim: 融合特征的维度 (默认:256)
          - num_classes: 输出类别数
        """
        super().__init__()
        self.seq_branch_encoder = kwargs.get('seq_branch_encoder', None)
        self.img_branch_encoder = kwargs.get('img_branch_encoder', None)
        if self.seq_branch_encoder is None:
            raise ValueError("请提供序列分支的编码器")
        if self.img_branch_encoder is None:
            raise ValueError("请提供图像分支的编码器")

        # 获取参数
        seq_dim = kwargs.get('seq_dim', 128)
        img_dim = kwargs.get('img_dim', 128)
        fusion_dim = kwargs.get('fusion_dim', 256)
        num_classes = kwargs.get('num_classes', None)
        if num_classes is None:
            raise ValueError("请提供输出类别数")

        # 特征适配层 (解决维度不匹配问题)
        self.seq_adapter = nn.Sequential(
            nn.Linear(seq_dim, fusion_dim),
            nn.ReLU()
        ) if seq_dim != fusion_dim else nn.Identity()

        self.img_adapter = nn.Sequential(
            nn.Linear(img_dim, fusion_dim),
            nn.ReLU()
        ) if img_dim != fusion_dim else nn.Identity()

        # 温度参数 (自适应特征缩放)
        self.temperature = nn.Parameter(torch.tensor(1.0))

        # 特征融合模块
        self.feature_fusion = EnhancedCrossAttentionFusion(
            seq_dim=fusion_dim,
            img_dim=fusion_dim,
            fusion_dim=fusion_dim,
            num_heads=kwargs.get('num_heads', 8),
            dropout=kwargs.get('dropout', 0.1)
        )

        # 分类器
        self.seq_classifier = nn.Linear(seq_dim, num_classes)
        self.img_classifier = nn.Linear(img_dim, num_classes)
        self.classifier = nn.Sequential(
            nn.LayerNorm(fusion_dim),
            nn.Linear(fusion_dim, num_classes)
        )

        # 加载预训练权重
        self._load_pretrained_weights(kwargs)

        # 打印模型信息
        print(f"模型初始化完成 - 融合维度: {fusion_dim}, 类别数: {num_classes}")

    def _load_pretrained_weights(self, kwargs):
        """改进的预训练权重加载方法，支持多种权重格式"""

        def load_weights(encoder, path):
            if not path:
                return

            try:
                state = torch.load(path, map_location='cpu')

                # 处理不同的权重保存格式
                if 'model' in state:  # 完整模型保存
                    state = state['model']
                elif 'state_dict' in state:  # 训练检查点
                    state = state['state_dict']

                # 移除可能的模块前缀
                state = {k.replace('module.', ''): v for k, v in state.items()}

                # 创建新的状态字典，只包含匹配的键
                model_dict = encoder.state_dict()
                matched_state = {}
                for k, v in state.items():
                    if k in model_dict and v.shape == model_dict[k].shape:
                        matched_state[k] = v

                # 加载匹配的权重
                model_dict.update(matched_state)
                encoder.load_state_dict(model_dict)
                print(f"成功加载权重: {len(matched_state)}/{len(state)} 参数匹配")
            except Exception as e:
                print(f"加载预训练权重失败: {e}")

        # 加载序列分支权重
        load_weights(self.seq_branch_encoder, kwargs.get('seq_pretrained_path'))
        # 加载图像分支权重
        load_weights(self.img_branch_encoder, kwargs.get('img_pretrained_path'))

    def freeze_backbone(self, freeze_seq=True, freeze_img=True):
        """灵活的冻结策略"""
        for param in self.seq_branch_encoder.parameters():
            param.requires_grad = not freeze_seq
        for param in self.img_branch_encoder.parameters():
            param.requires_grad = not freeze_img

        # 总是训练融合模块和分类器
        for module in [self.seq_adapter, self.img_adapter, self.feature_fusion,
                       self.seq_classifier, self.img_classifier, self.classifier]:
            for param in module.parameters():
                param.requires_grad = True

        print(f"冻结状态: 序列分支={freeze_seq}, 图像分支={freeze_img}")

    def unfreeze_backbone(self):
        """解冻所有参数"""
        for param in self.parameters():
            param.requires_grad = True
        print("所有参数已解冻")

    def forward(self, img_branch_input, seq_branch_input, attention_mask=None):
        """
        前向传播
        :param img_branch_input: 图像输入 [B, C, H, W]
        :param seq_branch_input: 序列输入 [B, seq_len]
        :param attention_mask: 注意力掩码 [B, seq_len] (可选)
        :return: 包含多任务输出的字典
        """
        # 获取特征
        seq_features = self.seq_branch_encoder(seq_branch_input, attention_mask)  # [B, seq_dim]
        img_features = self.img_branch_encoder(img_branch_input)  # [B, img_dim]

        # 特征适配
        seq_adapted = self.seq_adapter(seq_features)  # [B, fusion_dim]
        img_adapted = self.img_adapter(img_features)  # [B, fusion_dim]

        # 温度缩放 (自适应特征缩放)
        seq_adapted = seq_adapted * torch.exp(self.temperature)
        img_adapted = img_adapted * torch.exp(self.temperature)

        # 特征融合
        fused_features = self.feature_fusion(seq_adapted, img_adapted)  # [B, fusion_dim]

        # 多任务输出
        seq_out = self.seq_classifier(seq_features)  # [B, num_classes]
        img_out = self.img_classifier(img_features)  # [B, num_classes]
        fusion_out = self.classifier(fused_features)  # [B, num_classes]

        return {
            "seq_out": seq_out,
            "img_out": img_out,
            "output": fusion_out,
            "features": {
                "seq": seq_features.detach(),  # 分离用于分析
                "img": img_features.detach(),
                "fused": fused_features.detach()
            }
        }


# 测试代码
if __name__ == '__main__':
    # 模拟编码器
    class DummySeqEncoder(nn.Module):
        def __init__(self, out_dim=128):
            super().__init__()
            self.out_dim = out_dim
            self.linear = nn.Linear(1024, out_dim)

        def forward(self, x, attention_mask=None):
            # 简化处理：如果是2D输入，直接投影
            if x.dim() == 2:
                return self.linear(x)
            # 如果是3D输入 (B, seq_len, dim)，取平均
            return self.linear(x.mean(dim=1))


    class DummyImgEncoder(nn.Module):
        def __init__(self, out_dim=128):
            super().__init__()
            self.out_dim = out_dim
            self.conv = nn.Conv2d(3, 16, 3, 1, 1)
            self.pool = nn.AdaptiveAvgPool2d(1)
            self.linear = nn.Linear(16, out_dim)

        def forward(self, x):
            x = self.conv(x)
            x = self.pool(x)
            return self.linear(x.view(x.size(0), -1))


    # 创建模型实例
    seq_encoder = DummySeqEncoder(out_dim=128)
    img_encoder = DummyImgEncoder(out_dim=256)  # 故意设置不同维度测试适配层

    model = MergedModel(
        seq_branch_encoder=seq_encoder,
        img_branch_encoder=img_encoder,
        seq_dim=128,
        img_dim=256,
        fusion_dim=512,
        num_classes=10,
        seq_pretrained_path=None,
        img_pretrained_path=None
    )

    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"模型总参数: {total_params:,}")
    print(f"可训练参数: {trainable_params:,}")

    # 测试冻结功能
    model.freeze_backbone(freeze_seq=True, freeze_img=False)

    # 验证输入输出
    try:
        dummy_img = torch.randn(2, 3, 32, 32)
        dummy_seq = torch.randn(2, 1024)  # 1D序列输入
        dummy_seq_3d = torch.randn(2, 64, 1024)  # 3D序列输入
        dummy_mask = torch.ones(2, 64)

        # 测试1D序列输入
        output = model(dummy_img, dummy_seq)
        print("\n1D序列输入测试通过")
        print(f"序列输出形状: {output['seq_out'].shape}")
        print(f"图像输出形状: {output['img_out'].shape}")
        print(f"融合输出形状: {output['fusion_out'].shape}")
        print(
            f"特征字典: seq={output['features']['seq'].shape}, img={output['features']['img'].shape}, fused={output['features']['fused'].shape}")

        # 测试3D序列输入
        output_3d = model(dummy_img, dummy_seq_3d, dummy_mask)
        print("\n3D序列输入测试通过")
        print(f"序列输出形状: {output_3d['seq_out'].shape}")

        # 特征一致性检查
        seq_cos = F.cosine_similarity(output['features']['seq'], output_3d['features']['seq'])
        print(f"不同序列输入的特征一致性: {seq_cos.mean().item():.4f}")

        # 测试温度参数
        print(f"初始温度值: {model.temperature.item():.4f}")

        # 测试任务权重
        print(f"初始任务权重: {F.softmax(model.alpha, dim=0).detach().numpy()}")

        print("所有测试通过!")
    except Exception as e:
        print(f"模型测试失败: {str(e)}")
        import traceback

        traceback.print_exc()