import pickle
# 验证两者是否相同
pcap_data = open('./Cridex.pcap.TCP_10-0-2-108_49171_93-189-89-83_8080.pcap', 'rb').read()
pkl_data = pickle.load(open('./Cridex.pcap.TCP_10-0-2-108_49171_93-189-89-83_8080.pkl', 'rb'))

# 比较前100字节
pcap_first_100 = [b for b in pcap_data[:100]]
pkl_first_100 = pkl_data[:100].tolist()

print("PCAP前100字节:", pcap_first_100)
print("PKL前100字节:", pkl_first_100)
print("是否相同:", pcap_first_100 == pkl_first_100)
