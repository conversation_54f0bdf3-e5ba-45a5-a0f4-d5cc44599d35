# -*- coding: utf-8 -*-
"""
Stage 1: Session-based PCAP Splitting (SplitCap-like functionality)
Splits PCAP files by sessions preserving ALL network layers (L2-L7)
Processes complete network packets similar to SplitCap tool
"""

import os
import logging
import socket
import struct
import time
from pathlib import Path
from collections import defaultdict
import dpkt
import tqdm


class SessionSplitter:
    """
    Session-based PCAP splitter that preserves ALL network layers (L2-L7)
    Similar to SplitCap tool - splits by sessions while maintaining complete packet data
    Processes Ethernet frames with all encapsulated protocols intact
    """

    def __init__(self, output_base_dir="./temp_files/splitpcap",
                 max_sessions=6000,
                 min_file_bytes=199,
                 max_file_bytes=1024*1024):
        """
        Initialize session splitter

        Args:
            output_base_dir: Base directory for output
            max_sessions: Maximum number of sessions to process per PCAP
            min_file_bytes: Minimum file size to keep (filter out empty sessions)
            max_file_bytes: Maximum file size to process (memory protection)
        """
        self.output_base_dir = Path(output_base_dir)
        self.max_sessions = max_sessions
        self.min_file_bytes = min_file_bytes
        self.max_file_bytes = max_file_bytes
        self.logger = self._setup_logger()


    def _setup_logger(self):
        """Setup logging"""
        logger = logging.getLogger('SessionSplitter')
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

    def _get_session_key(self, eth):
        """
        Generate session key for packet grouping - handles ALL network layers
        Creates bidirectional session keys for proper session grouping

        Args:
            eth: dpkt.ethernet.Ethernet object

        Returns:
            tuple: Session key for grouping packets into sessions
        """
        try:
            # Handle IP packets (most common case)
            if isinstance(eth.data, dpkt.ip.IP):
                ip = eth.data
                src_ip = socket.inet_ntoa(ip.src)
                dst_ip = socket.inet_ntoa(ip.dst)

                # Handle TCP packets
                if isinstance(ip.data, dpkt.tcp.TCP):
                    tcp = ip.data
                    src_port = tcp.sport
                    dst_port = tcp.dport
                    protocol = 6  # TCP protocol number

                # Handle UDP packets
                elif isinstance(ip.data, dpkt.udp.UDP):
                    udp = ip.data
                    src_port = udp.sport
                    dst_port = udp.dport
                    protocol = 17  # UDP protocol number

                # Handle ICMP packets
                elif isinstance(ip.data, dpkt.icmp.ICMP):
                    src_port = ip.data.type
                    dst_port = ip.data.code
                    protocol = 1  # ICMP protocol number

                # Handle other IP protocols
                else:
                    src_port = 0
                    dst_port = 0
                    protocol = ip.p

                # Create bidirectional session key (normalize direction)
                key1 = (src_ip, dst_ip, src_port, dst_port, protocol)
                key2 = (dst_ip, src_ip, dst_port, src_port, protocol)
                return min(key1, key2)

            # Handle non-IP packets (ARP, VLAN, etc.) - Layer 2 sessions
            else:
                src_mac = eth.src.hex()
                dst_mac = eth.dst.hex()
                eth_type = eth.type

                # Create bidirectional MAC-based session key
                key1 = (src_mac, dst_mac, eth_type)
                key2 = (dst_mac, src_mac, eth_type)
                return min(key1, key2)

        except Exception as e:
            # Fallback: use MAC addresses if packet parsing fails
            self.logger.debug(f"Session key generation failed, using MAC fallback: {e}")
            try:
                src_mac = eth.src.hex()
                dst_mac = eth.dst.hex()
                return min((src_mac, dst_mac), (dst_mac, src_mac))
            except:
                # Ultimate fallback: return a generic key
                return ("unknown", "unknown")


    def split_pcap_by_sessions(self, pcap_path, output_dir):
        """
        Split PCAP by sessions preserving ALL network layers (L2-L7)
        Similar to SplitCap functionality - maintains complete packet data
        """
        pcap_path = Path(pcap_path)
        output_dir = Path(output_dir)

        self.logger.info(f"Processing PCAP: {pcap_path}")

        try:
            # Create ALL layer directory (preserves complete packets)
            all_dir = output_dir / 'all'
            all_dir.mkdir(parents=True, exist_ok=True)

            sessions = defaultdict(list)
            file_size = pcap_path.stat().st_size

            # Open PCAP file for reading
            with open(pcap_path, 'rb') as f:
                pcap_reader = dpkt.pcap.Reader(f)

                # Get PCAP file type for writer
                pcap_type = pcap_reader.datalink()

                # Process packets with progress tracking
                with tqdm.tqdm(total=file_size, unit='B', unit_scale=True,
                               desc=f"Processing {pcap_path.name}") as pbar:
                    processed_bytes = 0

                    try:  # Add try-except block for file-level errors
                        for ts, buf in pcap_reader:
                            # Update progress
                            processed_bytes += len(buf)
                            pbar.update(processed_bytes - pbar.n)

                            try:
                                # Parse Ethernet frame (preserves ALL layers)
                                eth = dpkt.ethernet.Ethernet(buf)

                                # Process ALL types of packets (not just IP)
                                # This preserves Layer 2-7 data like SplitCap

                                # Get session key for any packet type
                                session_key = self._get_session_key(eth)
                                if session_key:
                                    # Stop adding new sessions if max reached
                                    if len(sessions) >= self.max_sessions and session_key not in sessions:
                                        continue

                                    # Store complete packet with timestamp and raw buffer
                                    # This preserves ALL network layers
                                    sessions[session_key].append((ts, buf))

                            except (dpkt.dpkt.UnpackError, dpkt.dpkt.NeedData) as e:
                                self.logger.debug(f"Skipping malformed packet: {e}")

                    except (dpkt.dpkt.NeedData, dpkt.dpkt.UnpackError) as e:
                        self.logger.error(f"Truncated/malformed PCAP {pcap_path}: {e}. Using packets read so far.")
                        # Advance progress bar to end of file
                        if file_size > pbar.n:
                            pbar.update(file_size - pbar.n)

            self.logger.info(f"Found {len(sessions)} sessions")

            # Write sessions to files with size filtering
            session_pbar = tqdm.tqdm(total=len(sessions), desc="Saving sessions")
            saved_sessions = 0

            for session_idx, (session_key, session_packets) in enumerate(sessions.items()):
                session_name = f"session_{session_idx:04d}"
                layer_file = all_dir / f"{session_name}.pcap"

                # Create temporary file to check size
                temp_file = layer_file.with_suffix('.tmp')

                try:
                    # Create PCAP writer with same settings as input
                    with open(temp_file, 'wb') as f_out:
                        pcap_writer = dpkt.pcap.Writer(f_out, snaplen=65535, linktype=pcap_type)

                        # Write all packets in session (preserves ALL layers)
                        for ts, buf in session_packets:
                            pcap_writer.writepkt(buf, ts)

                    # Check file size and keep only valid sessions
                    file_size = temp_file.stat().st_size
                    if self.min_file_bytes <= file_size <= self.max_file_bytes:
                        temp_file.rename(layer_file)
                        saved_sessions += 1
                    else:
                        temp_file.unlink()  # Remove files that are too small or too large

                except Exception as e:
                    self.logger.warning(f"Failed to save session {session_name}: {e}")
                    if temp_file.exists():
                        temp_file.unlink()

                session_pbar.update(1)

            session_pbar.close()
            self.logger.info(f"Session splitting completed for {pcap_path.name}")
            self.logger.info(f"Saved {saved_sessions}/{len(sessions)} sessions (filtered by size)")

        except Exception as e:
            self.logger.error(f"Failed to process {pcap_path}: {e}")
            raise

    def process_directory(self, input_dir, preserve_structure=True):
        """
        Process all PCAP files in a directory with progress tracking

        Args:
            input_dir: Input directory containing PCAP files
            preserve_structure: Whether to preserve directory structure
        """
        input_dir = Path(input_dir)

        if not input_dir.exists():
            raise FileNotFoundError(f"Input directory not found: {input_dir}")

        # Find all PCAP files (including subdirectories)
        pcap_files = []
        for root, _, files in os.walk(input_dir):
            for file in files:
                if file.endswith(('.pcap', '.pcapng')):
                    pcap_files.append(Path(root) / file)

        if not pcap_files:
            self.logger.warning(f"No PCAP files found in {input_dir}")
            return

        self.logger.info(f"Found {len(pcap_files)} PCAP files to process")

        # Create master progress bar
        with tqdm.tqdm(total=len(pcap_files), desc="Overall Progress") as master_pbar:
            for pcap_file in pcap_files:
                try:
                    master_pbar.set_description(f"Processing: {pcap_file.name}")

                    # Calculate output path
                    if preserve_structure:
                        rel_path = pcap_file.relative_to(input_dir)
                        output_dir = self.output_base_dir / rel_path.parent / rel_path.stem
                    else:
                        output_dir = self.output_base_dir / pcap_file.stem

                    self.split_pcap_by_sessions(pcap_file, output_dir)

                except Exception as e:
                    self.logger.error(f"Failed to process {pcap_file}: {e}")
                finally:
                    master_pbar.update(1)



if __name__ == "__main__":
    # Example usage - SplitCap-like functionality
    splitter = SessionSplitter(
        output_base_dir="./temp_files/splitpcap_all_layers",
        max_sessions=6000,
        min_file_bytes=199,      # Filter out very small sessions
        max_file_bytes=1024*1024 # Limit session file size (1MB)
    )

    # Process all PCAPs in directory preserving ALL network layers
    splitter.process_directory("./pcap_dataset")