#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple High-Performance PCAP Anonymizer
Only anonymizes IP and MAC addresses - nothing else
Optimized for maximum speed and minimal memory usage
"""

import os
import logging
import socket
import hashlib
from pathlib import Path
import dpkt
import tqdm


class TrafficAnonymizer:
    """
    Ultra-simple anonymizer that only changes IP and MAC addresses
    Designed for maximum performance with minimal overhead
    """

    def __init__(self, input_base_dir="./temp_files/splitpcap", 
                 output_base_dir="./temp_files/anonymous"):
        """
        Initialize simple anonymizer

        Args:
            input_base_dir: Input directory with PCAP files
            output_base_dir: Output directory for anonymized files
        """
        self.input_base_dir = Path(input_base_dir)
        self.output_base_dir = Path(output_base_dir)
        self.logger = self._setup_logger()

    def _setup_logger(self):
        """Setup minimal logging"""
        logger = logging.getLogger('TrafficAnonymizer')
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

    def _hash_to_ip(self, original_ip):
        """Convert IP to anonymized IP using hash (deterministic)"""
        # Use MD5 hash for deterministic mapping
        hash_bytes = hashlib.md5(original_ip.encode()).digest()[:4]
        # Map to private IP range 10.x.x.x
        return f"10.{hash_bytes[1]}.{hash_bytes[2]}.{hash_bytes[3]}"

    def _hash_to_mac(self, original_mac):
        """Convert MAC to anonymized MAC using hash (deterministic)"""
        # Use MD5 hash for deterministic mapping
        hash_bytes = hashlib.md5(original_mac.encode()).digest()[:6]
        # Set locally administered bit (02:xx:xx:xx:xx:xx)
        hash_bytes = bytes([0x02]) + hash_bytes[1:]
        return ':'.join(f'{b:02x}' for b in hash_bytes)

    def anonymize_packet(self, ts, buf, ip_cache, mac_cache):
        """
        Anonymize single packet - IP and MAC only
        Uses caching for better performance
        """
        try:
            # Parse Ethernet frame
            eth = dpkt.ethernet.Ethernet(buf)

            # Anonymize MAC addresses
            src_mac = eth.src.hex(':')
            dst_mac = eth.dst.hex(':')

            # Use cache for MAC addresses
            if src_mac not in mac_cache:
                mac_cache[src_mac] = self._hash_to_mac(src_mac)
            if dst_mac not in mac_cache:
                mac_cache[dst_mac] = self._hash_to_mac(dst_mac)

            # Update MAC addresses
            eth.src = bytes.fromhex(mac_cache[src_mac].replace(':', ''))
            eth.dst = bytes.fromhex(mac_cache[dst_mac].replace(':', ''))

            # Anonymize IP addresses if present
            if isinstance(eth.data, dpkt.ip.IP):
                ip = eth.data
                src_ip = socket.inet_ntoa(ip.src)
                dst_ip = socket.inet_ntoa(ip.dst)

                # Use cache for IP addresses
                if src_ip not in ip_cache:
                    ip_cache[src_ip] = self._hash_to_ip(src_ip)
                if dst_ip not in ip_cache:
                    ip_cache[dst_ip] = self._hash_to_ip(dst_ip)

                # Update IP addresses
                ip.src = socket.inet_aton(ip_cache[src_ip])
                ip.dst = socket.inet_aton(ip_cache[dst_ip])

                # Clear checksums (let network stack recalculate)
                ip.sum = 0
                if hasattr(ip.data, 'sum'):
                    ip.data.sum = 0

            return ts, bytes(eth)

        except Exception:
            # Return original packet if parsing fails
            return ts, buf

    def anonymize_file(self, input_file, output_file):
        """
        Anonymize single PCAP file
        """
        input_file = Path(input_file)
        output_file = Path(output_file)

        # Create output directory
        output_file.parent.mkdir(parents=True, exist_ok=True)

        # Local caches for this file (each file gets unique mappings)
        ip_cache = {}
        mac_cache = {}

        try:
            with open(input_file, 'rb') as f_in:
                reader = dpkt.pcap.Reader(f_in)
                linktype = reader.datalink()

                with open(output_file, 'wb') as f_out:
                    writer = dpkt.pcap.Writer(f_out, snaplen=65535, linktype=linktype)

                    # Process all packets
                    packet_count = 0
                    for ts, buf in reader:
                        new_ts, new_buf = self.anonymize_packet(ts, buf, ip_cache, mac_cache)
                        writer.writepkt(new_buf, new_ts)
                        packet_count += 1

            return True, packet_count, len(ip_cache), len(mac_cache)

        except Exception as e:
            self.logger.error(f"Failed to anonymize {input_file}: {e}")
            return False, 0, 0, 0

    def process_directory(self):
        """
        Process all PCAP files in directory
        """
        if not self.input_base_dir.exists():
            raise FileNotFoundError(f"Input directory not found: {self.input_base_dir}")

        # Find all PCAP files
        pcap_files = []
        for root, dirs, files in os.walk(self.input_base_dir):
            for file in files:
                if file.endswith('.pcap'):
                    input_path = Path(root) / file
                    rel_path = input_path.relative_to(self.input_base_dir)
                    output_path = self.output_base_dir / rel_path
                    pcap_files.append((input_path, output_path))

        if not pcap_files:
            self.logger.warning("No PCAP files found")
            return

        self.logger.info(f"Found {len(pcap_files)} PCAP files to anonymize")

        # Process files with progress bar
        total_packets = 0
        total_ips = 0
        total_macs = 0
        successful = 0

        with tqdm.tqdm(total=len(pcap_files), desc="Anonymizing files") as pbar:
            for input_file, output_file in pcap_files:
                pbar.set_description(f"Processing {input_file.name}")
                
                success, packets, ips, macs = self.anonymize_file(input_file, output_file)
                
                if success:
                    successful += 1
                    total_packets += packets
                    total_ips += ips
                    total_macs += macs
                
                pbar.update(1)

        # Summary
        self.logger.info("=" * 50)
        self.logger.info("ANONYMIZATION COMPLETED")
        self.logger.info("=" * 50)
        self.logger.info(f"Files processed: {successful}/{len(pcap_files)}")
        self.logger.info(f"Total packets: {total_packets:,}")
        self.logger.info(f"Unique IPs anonymized: {total_ips:,}")
        self.logger.info(f"Unique MACs anonymized: {total_macs:,}")
        self.logger.info(f"Output directory: {self.output_base_dir}")

    def process_single_directory(self, session_dir):
        """
        Process a single session directory (for integration with pipeline)
        """
        session_dir = Path(session_dir)
        if not session_dir.exists():
            return

        # Calculate output directory
        rel_path = session_dir.relative_to(self.input_base_dir)
        output_dir = self.output_base_dir / rel_path

        # Find PCAP files in 'all' subdirectory
        all_dir = session_dir / 'all'
        if not all_dir.exists():
            return

        pcap_files = list(all_dir.glob('*.pcap'))
        if not pcap_files:
            return

        # Create output 'all' directory
        output_all_dir = output_dir / 'all'
        output_all_dir.mkdir(parents=True, exist_ok=True)

        # Process each PCAP file
        for pcap_file in pcap_files:
            output_file = output_all_dir / pcap_file.name
            self.anonymize_file(pcap_file, output_file)


class FastAnonymizer:
    """
    Even faster version using pre-computed hash tables
    """
    
    def __init__(self):
        # Pre-compute hash mappings for common addresses
        self.ip_map = {}
        self.mac_map = {}
    
    def anonymize_ip(self, ip_str):
        """Fast IP anonymization with caching"""
        if ip_str not in self.ip_map:
            hash_val = int(hashlib.md5(ip_str.encode()).hexdigest()[:8], 16)
            self.ip_map[ip_str] = f"10.{(hash_val >> 16) & 0xFF}.{(hash_val >> 8) & 0xFF}.{hash_val & 0xFF}"
        return self.ip_map[ip_str]
    
    def anonymize_mac(self, mac_str):
        """Fast MAC anonymization with caching"""
        if mac_str not in self.mac_map:
            hash_val = int(hashlib.md5(mac_str.encode()).hexdigest()[:12], 16)
            self.mac_map[mac_str] = f"02:{(hash_val >> 32) & 0xFF:02x}:{(hash_val >> 24) & 0xFF:02x}:{(hash_val >> 16) & 0xFF:02x}:{(hash_val >> 8) & 0xFF:02x}:{hash_val & 0xFF:02x}"
        return self.mac_map[mac_str]


if __name__ == "__main__":
    # Example usage - compatible with existing pipeline
    import argparse

    parser = argparse.ArgumentParser(description='PCAP Processing Pipeline')
    parser.add_argument('--input', '-i', default='./pcap_dataset',
                        help='Input directory containing PCAP files')
    parser.add_argument('--output', '-o', default='./img_dataset/ISAC218',
                        help='Output directory for RGB images')
    anonymizer = TrafficAnonymizer(
        input_base_dir=parser.parse_args().input,
        output_base_dir=parser.parse_args().output
    )

    # Process all files
    anonymizer.process_directory()
