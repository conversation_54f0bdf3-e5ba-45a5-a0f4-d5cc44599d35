# -*- coding: utf-8 -*-
"""
Stage 2: High-Performance Traffic Anonymization using dpkt - Fixed Checksums
Anonymizes MAC and IP addresses while preserving network structure
"""

import os
import logging
import random
import socket
import struct
from pathlib import Path
from collections import defaultdict
import dpkt
import tqdm
import concurrent.futures
import multiprocessing
import gc
from concurrent.futures import ThreadPoolExecutor


class TrafficAnonymizer:
    """
    High-performance traffic anonymizer using dpkt for packet processing
    Only anonymizes MAC and IP addresses while preserving layer relationships
    """

    def __init__(self, input_base_dir="./temp_files/splitpcap", output_base_dir="./temp_files/anonymous"):
        """
        Initialize traffic anonymizer

        Args:
            input_base_dir: Base directory containing split PCAP files
            output_base_dir: Base directory for anonymized output
        """
        self.input_base_dir = Path(input_base_dir)
        self.output_base_dir = Path(output_base_dir)
        self.logger = self._setup_logger()

    def _setup_logger(self):
        """Setup logging"""
        logger = logging.getLogger('TrafficAnonymizer')
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

    def _generate_ip(self):
        """Generate random IP in private range"""
        return f"10.{random.randint(1, 254)}.{random.randint(1, 254)}.{random.randint(1, 254)}"

    def _generate_mac(self):
        """Generate random MAC with locally administered bit set"""
        octets = [0x02] + [random.randint(0, 255) for _ in range(5)]
        return ':'.join(f"{x:02x}" for x in octets)

    def _validate_pcap_file(self, pcap_path):
        """Simple validation that PCAP file is readable"""
        try:
            with open(pcap_path, 'rb') as f:
                reader = dpkt.pcap.Reader(f)
                next(reader)  # Just try to read one packet
            return True
        except Exception as e:
            self.logger.warning(f"PCAP validation failed for {pcap_path}: {e}")
            return False

    def _update_transport_checksum(self, ip):
        """
        Update TCP/UDP checksum after IP address change
        Includes pseudo-header with source/destination IP addresses
        """
        try:
            if isinstance(ip.data, dpkt.tcp.TCP):
                tcp = ip.data
                # Set checksum to 0 before recalculating
                tcp.sum = 0

                # Calculate correct TCP checksum including pseudo-header
                tcp_bin = bytes(tcp)

                # Create pseudo-header (src_ip, dst_ip, 0, proto, tcp_len)
                pseudo_header = struct.pack('!4s4sBBH',
                                            ip.src,
                                            ip.dst,
                                            0,
                                            ip.p,
                                            len(tcp_bin))

                # Calculate checksum over pseudo-header + tcp
                tcp.sum = dpkt.in_cksum(pseudo_header + tcp_bin)

            elif isinstance(ip.data, dpkt.udp.UDP):
                udp = ip.data
                # Set checksum to 0 before recalculating
                udp.sum = 0

                # Calculate correct UDP checksum including pseudo-header
                udp_bin = bytes(udp)

                # Create pseudo-header (src_ip, dst_ip, 0, proto, udp_len)
                pseudo_header = struct.pack('!4s4sBBH',
                                            ip.src,
                                            ip.dst,
                                            0,
                                            ip.p,
                                            len(udp_bin))

                # Calculate checksum over pseudo-header + udp
                checksum = dpkt.in_cksum(pseudo_header + udp_bin)
                # If checksum is 0, set to 0xffff (all ones) as per UDP spec
                udp.sum = 0xffff if checksum == 0 else checksum

        except Exception as e:
            self.logger.debug(f"Transport checksum update failed: {e}")
            # If checksum calculation fails, set to 0 (disabled)
            if isinstance(ip.data, dpkt.tcp.TCP):
                ip.data.sum = 0
            elif isinstance(ip.data, dpkt.udp.UDP):
                ip.data.sum = 0

    def _anonymize_packet_dpkt(self, ts, buf, ip_map, mac_map):
        """
        Anonymize a single packet using dpkt - optimized for performance

        Args:
            ts: Packet timestamp
            buf: Raw packet bytes
            ip_map: Current IP mapping dict
            mac_map: Current MAC mapping dict

        Returns:
            tuple: (timestamp, anonymized packet bytes)
        """
        try:
            # Parse Ethernet frame
            eth = dpkt.ethernet.Ethernet(buf)

            # Anonymize MAC addresses
            src_mac = eth.src.hex(':')
            dst_mac = eth.dst.hex(':')

            if src_mac not in mac_map:
                mac_map[src_mac] = self._generate_mac()
            if dst_mac not in mac_map:
                mac_map[dst_mac] = self._generate_mac()

            eth.src = bytes.fromhex(mac_map[src_mac].replace(':', ''))
            eth.dst = bytes.fromhex(mac_map[dst_mac].replace(':', ''))

            # Anonymize IP addresses if present
            if isinstance(eth.data, dpkt.ip.IP):
                ip = eth.data

                # Save original IP addresses for checksum update
                orig_src_ip = ip.src
                orig_dst_ip = ip.dst

                # Anonymize source IP
                src_ip = socket.inet_ntoa(ip.src)
                if src_ip not in ip_map:
                    ip_map[src_ip] = self._generate_ip()
                ip.src = socket.inet_aton(ip_map[src_ip])

                # Anonymize destination IP
                dst_ip = socket.inet_ntoa(ip.dst)
                if dst_ip not in ip_map:
                    ip_map[dst_ip] = self._generate_ip()
                ip.dst = socket.inet_aton(ip_map[dst_ip])

                # Recalculate IP checksum
                ip.sum = 0
                ip.sum = dpkt.in_cksum(bytes(ip)[:ip.hl * 4])  # Only IP header

                # Only update transport checksum if IP changed
                if orig_src_ip != ip.src or orig_dst_ip != ip.dst:
                    self._update_transport_checksum(ip)

            return ts, bytes(eth)

        except (dpkt.dpkt.UnpackError, dpkt.dpkt.NeedData) as e:
            self.logger.debug(f"Skipping malformed packet: {e}")
            return ts, buf
        except Exception as e:
            self.logger.warning(f"Packet anonymization failed: {e}")
            return ts, buf

    def process_single_pcap(self, file_path, output_file):
        """
        Process a single pcap file using dpkt for high-performance processing

        Args:
            file_path: Path to input PCAP file
            output_file: Path to output PCAP file
        """
        ip_map = {}
        mac_map = {}

        try:
            with open(file_path, 'rb') as f_in:
                # Get PCAP file properties
                reader = dpkt.pcap.Reader(f_in)
                linktype = reader.datalink()

                with open(output_file, 'wb') as f_out:
                    writer = dpkt.pcap.Writer(f_out, snaplen=65535, linktype=linktype)

                    for ts, buf in reader:
                        ts, anon_buf = self._anonymize_packet_dpkt(ts, buf, ip_map, mac_map)
                        writer.writepkt(anon_buf, ts)

            # Optional validation
            if not self._validate_pcap_file(output_file):
                self.logger.warning(f"Validation failed, using original: {file_path}")
                output_file.unlink(missing_ok=True)
                with open(file_path, 'rb') as f_in, open(output_file, 'wb') as f_out:
                    f_out.write(f_in.read())

            return True, file_path, None

        except Exception as e:
            self.logger.error(f"Failed to process {file_path}: {e}")
            if output_file.exists():
                output_file.unlink()
            with open(file_path, 'rb') as f_in, open(output_file, 'wb') as f_out:
                f_out.write(f_in.read())
            return False, file_path, str(e)

    def anonymize_session_files(self, session_dir):
        """
        Process all PCAP files in a session directory using parallel processing

        Args:
            session_dir: Directory containing AL subdirectory with PCAP files
        """
        session_dir = Path(session_dir)
        if not session_dir.exists():
            self.logger.warning(f"Session directory not found: {session_dir}")
            return 0

        # Prepare output path
        rel_path = session_dir.relative_to(self.input_base_dir)
        output_dir = self.output_base_dir / rel_path
        original_pcap_name = session_dir.name

        # Get AL directory
        al_dir = session_dir / "al"
        if not al_dir.exists():
            self.logger.warning(f"AL directory not found: {al_dir}")
            return 0

        # Prepare file processing list
        file_tasks = []
        for file_path in al_dir.glob("*.pcap"):
            output_file = output_dir / 'al' / file_path.name
            output_file.parent.mkdir(parents=True, exist_ok=True)
            file_tasks.append((file_path, output_file))

        # Process files in parallel
        processed_count = 0
        with ThreadPoolExecutor(max_workers=multiprocessing.cpu_count()) as executor:
            futures = [executor.submit(self.process_single_pcap, fp, op) for fp, op in file_tasks]
            for future in concurrent.futures.as_completed(futures):
                success, input_path, error = future.result()
                if success:
                    processed_count += 1

        self.logger.info(f"Completed {processed_count}/{len(file_tasks)} files in {original_pcap_name}")
        return processed_count

    def process_directory(self):
        """Process all session directories with progress tracking"""
        if not self.input_base_dir.exists():
            raise FileNotFoundError(f"Input directory not found: {self.input_base_dir}")

        # Find all session directories with AL layer
        session_dirs = []
        for root, _, _ in os.walk(self.input_base_dir):
            root_path = Path(root)
            if (root_path / 'al').exists():
                session_dirs.append(root_path)

        if not session_dirs:
            self.logger.warning("No valid directories found")
            return

        self.logger.info(f"Found {len(session_dirs)} session directories to process")

        # Count total files for progress tracking
        total_files = 0
        for session_dir in session_dirs:
            al_dir = session_dir / "al"
            if al_dir.exists():
                total_files += len(list(al_dir.glob("*.pcap")))

        # Process sessions with progress bar
        with tqdm.tqdm(total=total_files, desc="Overall Progress") as pbar:
            with ThreadPoolExecutor(max_workers=multiprocessing.cpu_count() // 2) as executor:
                futures = {executor.submit(self.anonymize_session_files, session_dir): session_dir
                           for session_dir in session_dirs}

                for future in concurrent.futures.as_completed(futures):
                    try:
                        processed = future.result()
                        pbar.update(processed)
                    except Exception as e:
                        session_dir = futures[future]
                        self.logger.error(f"Failed to process {session_dir}: {e}")
                        # Estimate failure by counting files in session
                        al_dir = session_dir / "al"
                        if al_dir.exists():
                            pbar.update(len(list(al_dir.glob("*.pcap"))))

        self.logger.info("Directory anonymization completed")
        self.logger.info("Anonymization summary:")
        self.logger.info("- Only AL layer processed (PCAP files)")
        self.logger.info("- MAC addresses randomized (data link layer)")
        self.logger.info("- IP addresses randomized (IP layer)")
        self.logger.info("- All checksums properly recalculated")
        self.logger.info("- Each file has independent anonymization mappings")


if __name__ == "__main__":
    # Example usage
    anonymizer = TrafficAnonymizer(
        input_base_dir="./temp_files/splitpcap_dpkt",
        output_base_dir="./temp_files/anonymous"
    )

    # Process all session directories
    anonymizer.process_directory()