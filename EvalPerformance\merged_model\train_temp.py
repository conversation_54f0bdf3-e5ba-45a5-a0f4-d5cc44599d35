import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import datasets, transforms, models
from torch.utils.data import DataLoader
import os
import yaml
import argparse
from tqdm import tqdm  # 添加tqdm进度条支持
from torch.utils.tensorboard import SummaryWriter  # 添加TensorBoard支持
import time
import datetime


def parse_args():
    parser = argparse.ArgumentParser(description='Finetuning with YAML config')
    parser.add_argument('-c','--config', type=str, required=True,
                        help='Path to YAML configuration file')
    return parser.parse_args()


def load_config(config_path):
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


# 解析命令行参数
args = parse_args()

# 加载YAML配置
config = load_config(args.config)

# 设置GPU设备
if 'CUDA_VISIBLE_DEVICES' in config:
    os.environ['CUDA_VISIBLE_DEVICES'] = str(config['CUDA_VISIBLE_DEVICES'])

# 使用配置中的参数
img_train_data_path = config['img_train_data_path']
img_val_data_path = config['img_val_data_path']
seq_train_data_path = config['seq_train_data_path']
seq_val_data_path = config['seq_val_data_path']
img_pretrained_path = config.get('img_pretrained_path', None)
seq_pretrained_path = config.get('seq_pretrained_path', None)
batch_size = config['batch_size']
learning_rate = config['learning_rate']
freeze_train = config['freeze_train']
finetune_epochs = config['finetune_epochs']
freeze_epoch = config['freeze_epoch']
seq_length = config.get('seq_length', 1024)
fusion_dim = config.get('fusion_dim', 256)
save_dir = config['save_dir']
log_name = config['log_name']
img_size = config.get('img_size', 32)
# 创建保存目录
os.makedirs(save_dir, exist_ok=True)

# 设置TensorBoard日志目录
log_dir = os.path.join(save_dir, "logs",datetime.datetime.now().strftime('%Y_%m_%d'),log_name)
os.makedirs(log_dir, exist_ok=True)
writer = SummaryWriter(log_dir=log_dir)  # 创建TensorBoard写入器

# 导入双分支数据集
from dataset import DualBranchDataset
# 创建双分支数据加载器
img_transform = transforms.Compose([
    transforms.Resize((img_size, img_size)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])
train_dataset = DualBranchDataset(
    img_root=img_train_data_path,
    seq_root=seq_train_data_path,
    img_branch_transform=img_transform,
    seq_length=seq_length
)
val_dataset = DualBranchDataset(
    img_root=img_val_data_path,
    seq_root=seq_val_data_path,
    img_branch_transform=img_transform,
    seq_length=seq_length
)
num_classes = len(train_dataset.classes)

train_loader = torch.utils.data.DataLoader(
    train_dataset,
    batch_size=batch_size,
    shuffle=True,
    num_workers=4,
    pin_memory=True
)
val_loader = torch.utils.data.DataLoader(
    val_dataset,
    batch_size=batch_size,
    shuffle=False,
    num_workers=4,
    pin_memory=True
)


def validate(model, loader, device, epoch, writer=None):
    model.eval()
    correct = 0
    total = 0
    val_loss = 0.0
    criterion = nn.CrossEntropyLoss()
    passbar = tqdm(loader, desc=f'Validation Epoch {epoch + 1}', leave=False)
    with torch.no_grad():
        for batch in passbar:
            images = batch['image'].to(device)
            sequences = batch['sequence'].to(device)
            labels = batch['label'].to(device)

            outputs = model(img_branch_input=images, seq_branch_input=sequences)
            loss = criterion(outputs['output'], labels)
            val_loss += loss.item() * images.size(0)
            _, predicted = torch.max(outputs['output'].data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()

    val_acc = 100 * correct / total
    val_loss = val_loss / len(loader.dataset)

    # 记录验证结果到TensorBoard
    if writer:
        writer.add_scalar('Loss/val', val_loss, epoch)
        writer.add_scalar('Accuracy/val', val_acc, epoch)

    return val_acc, val_loss


def load_pretrained_weights(model, img_pretrained_path, seq_pretrained_path):
    """加载双分支预训练权重"""

    # 加载图像分支预训练权重
    if img_pretrained_path is not None:
        try:
            print(f"Loading image branch weights from: {img_pretrained_path}")
            checkpoint = torch.load(img_pretrained_path, map_location='cpu')

            # 处理不同格式的checkpoint
            if 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            elif 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            elif 'model' in checkpoint:
                state_dict = checkpoint['model']
            else:
                state_dict = checkpoint

            # 移除可能存在的module.前缀
            state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
            # 过滤出图像分支的权重
            img_state_dict = {k.replace('backbone.', ''): v for k, v in state_dict.items()
                             if k.startswith('backbone') or not k.startswith('classifier')}

            # 加载到图像分支编码器
            msg = model.img_branch_encoder.load_state_dict(img_state_dict, strict=False)
            print(f'Image branch - Missing keys: {len(msg.missing_keys)}')
            print(f'Image branch - Unexpected keys: {len(msg.unexpected_keys)}')

        except Exception as e:
            print(f"Failed to load image branch weights: {e}")

    # 加载序列分支预训练权重
    if seq_pretrained_path is not None:
        try:
            print(f"Loading sequence branch weights from: {seq_pretrained_path}")
            checkpoint = torch.load(seq_pretrained_path, map_location='cpu')

            # 处理不同格式的checkpoint
            if 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            elif 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            elif 'model' in checkpoint:
                state_dict = checkpoint['model']
            else:
                state_dict = checkpoint

            # 移除可能存在的module.前缀
            state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
            # 不加载分类头的权重
            seq_state_dict = {k: v for k, v in state_dict.items()
                             if not k.startswith('classifier') and not k.startswith('final_projection')}

            # 加载到序列分支编码器
            msg = model.seq_branch_encoder.load_state_dict(seq_state_dict, strict=False)
            print(f'Sequence branch - Missing keys: {len(msg.missing_keys)}')
            print(f'Sequence branch - Unexpected keys: {len(msg.unexpected_keys)}')

        except Exception as e:
            print(f"Failed to load sequence branch weights: {e}")

    return model


if __name__ == '__main__':
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # 导入模型组件
    from torchvision.models import resnet18
    import torch.nn as nn
    from bert_encoder import BertEncoderConfig, BertEncoder
    from model import MergedModel

    # 创建图像分支编码器
    img_branch = resnet18(pretrained=False)
    img_dim = img_branch.fc.in_features
    img_branch.fc = nn.Identity()  # 移除分类头

    # 创建序列分支编码器
    seq_config = BertEncoderConfig()
    seq_branch = BertEncoder(seq_config)
    seq_dim = seq_branch.final_projection.in_features
    seq_branch.final_projection = nn.Identity()  # 移除投影头
    # 创建融合模型
    model = MergedModel(
        img_branch_encoder=img_branch,
        seq_branch_encoder=seq_branch,
        img_dim=img_dim,
        seq_dim=seq_dim,
        fusion_dim=fusion_dim,
        num_classes=num_classes
    )

    model = model.to(device)
    print(f"Model created with {num_classes} classes")

    # 加载预训练权重
    model = load_pretrained_weights(model, img_pretrained_path, seq_pretrained_path)

    if freeze_train:
        model.freeze_backbone()
        # 初始只优化分类器
        optimizer = optim.Adam(model.classifier.parameters(), lr=learning_rate)
    else:
        # 如果不冻结，优化所有参数
        optimizer = optim.Adam(model.parameters(), lr=learning_rate)

    # 添加学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=len(train_loader), eta_min=0,
                                                           last_epoch=-1)

    criterion = nn.CrossEntropyLoss()
    best_acc = 0.0

    print("Starting training...")
    print(f"TensorBoard logs saved to: {log_dir}")

    # 训练循环
    for epoch in range(finetune_epochs):
        start_time = time.time()

        # 解冻逻辑
        if freeze_train and epoch == freeze_epoch:
            model.unfreeze_backbone()
            # 解冻后需要更新优化器以包含所有参数
            optimizer = optim.Adam(model.parameters(), lr=learning_rate)
            print("Optimizer reset for full model training")

        model.train()
        running_loss = 0.0

        # 使用tqdm封装训练过程
        train_bar = tqdm(train_loader, desc=f'Epoch {epoch + 1}/{finetune_epochs}', leave=False)
        for batch in train_bar:
            images = batch['image'].to(device)
            sequences = batch['sequence'].to(device)
            labels = batch['label'].to(device)

            optimizer.zero_grad()
            outputs = model(img_branch_input=images, seq_branch_input=sequences)
            loss = (criterion(outputs['output'], labels)
                    + 0.5*criterion(outputs['img_out'], labels)
                    + 0.5*criterion(outputs['seq_out'], labels))
            loss.backward()
            optimizer.step()

            running_loss += loss.item() * images.size(0)
            # 更新进度条显示当前loss
            train_bar.set_postfix(loss=loss.item())

        # 计算平均训练损失
        epoch_loss = running_loss / len(train_loader.dataset)

        # 记录训练损失到TensorBoard
        writer.add_scalar('Loss/train', epoch_loss, epoch)

        # 验证
        val_acc, val_loss = validate(model, val_loader, device, epoch, writer)

        # 更新学习率
        scheduler.step(val_acc)

        # 记录当前学习率
        current_lr = optimizer.param_groups[0]['lr']
        writer.add_scalar('Learning Rate', current_lr, epoch)

        epoch_time = time.time() - start_time

        print(f'Epoch [{epoch + 1}/{finetune_epochs}] '
              f'Time: {epoch_time:.1f}s '
              f'Loss: {epoch_loss:.4f} '
              f'Val Loss: {val_loss:.4f} '
              f'Val Acc: {val_acc:.2f}% '
              f'LR: {current_lr:.6f}')

        # 保存最佳模型
        if val_acc > best_acc:
            best_acc = val_acc
            print(f"New best model accuracy: {best_acc:.2f}")

    # 关闭TensorBoard写入器
    writer.close()
    print(f"Training complete. Best validation accuracy: {best_acc:.2f}%")
    print(f"TensorBoard logs saved to: {log_dir}")
    print(f"To view results, run: tensorboard --logdir={log_dir}")