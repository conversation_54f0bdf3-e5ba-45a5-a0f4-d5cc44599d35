#!/usr/bin/env python3
"""
简化版本：读取PCAP字节流和PKL文件内容
"""

import pickle
import sys


def read_pcap_simple(pcap_file):
    """简单读取PCAP文件为字节"""
    try:
        with open(pcap_file, 'rb') as f:
            data = f.read()
            return data
    except Exception as e:
        print(f"读取PCAP文件错误: {e}")
        return None


def read_pkl_simple(pkl_file):
    """读取PKL文件"""
    try:
        with open(pkl_file, 'rb') as f:
            data = pickle.load(f)
            return data
    except Exception as e:
        print(f"读取PKL文件错误: {e}")
        return None


def main():
    if len(sys.argv) != 3:
        print("用法: python script.py <pcap_file> <pkl_file>")
        sys.exit(1)

    pcap_file = sys.argv[1]
    pkl_file = sys.argv[2]

    # 读取PCAP文件
    print("PCAP文件内容:")
    print("-" * 30)
    pcap_data = read_pcap_simple(pcap_file)
    if pcap_data:
        print(f"文件大小: {len(pcap_data)} 字节")
        print(f"前100字节: {pcap_data[:100]}")

    print("\nPKL文件内容:")
    print("-" * 30)
    pkl_data = read_pkl_simple(pkl_file)
    if pkl_data is not None:
        print(f"数据类型: {type(pkl_data)}")
        print(f"内容: {pkl_data}")


if __name__ == "__main__":
    main()
