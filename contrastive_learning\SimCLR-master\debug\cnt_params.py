import torch
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models import tangtangmodel  # 替换为实际路径

def detailed_param_report(model):
    import pandas as pd

    param_data = []
    for name, module in model.named_modules():
        if list(module.parameters()):  # 只统计有参数的模块
            params = sum(p.numel() for p in module.parameters())
            trainable = sum(p.numel() for p in module.parameters() if p.requires_grad)
            param_data.append({
                'Module': name,
                'Total Params': params,
                'Trainable': trainable,
                'Shape': next(module.parameters()).shape if hasattr(module, 'weight') else '-'
            })

    df = pd.DataFrame(param_data)
    df['% Total'] = df['Total Params'] / df['Total Params'].sum() * 100
    return df.sort_values('Total Params', ascending=False)


def main():
    # 1. 初始化模型
    from torchvision.models import resnet18
    import torch.nn as nn
    img_branch = resnet18(pretrained=False, num_classes=128)
    img_dim = img_branch.fc.in_features
    img_branch.fc = nn.Identity()
    from models.llama_encoder import LlamaEncoder, LlamaEncoderConfig
    cfg = LlamaEncoderConfig(
            out_dim=128,
            input_dim=1024,
            conv_kernel_size=16,  # 卷积核大小 = 步长
            conv_output_channels=256,  # 卷积输出通道数
            num_hidden_layers=16,  # 6->3
            num_attention_heads=8,
            num_key_value_heads=4,
            intermediate_size=1024,  # 256->128
        )
    seq_branch = LlamaEncoder(cfg)
    seq_dim = seq_branch.final_projection.in_features
    seq_branch.final_projection = nn.Identity()

    model = tangtangmodel.MergedModel(img_dim=img_dim,seq_dim=seq_dim,seq_branch_encoder=seq_branch,img_branch_encoder=img_branch)
    model.eval()
    # 2. 验证输入输出
    try:
        dummy_input = torch.randn(1, 3, 32, 32)
        output = model(dummy_input)
        print(f"模型测试通过，输出形状: {output.shape}")
    except Exception as e:
        print(f"模型前向传播错误: {str(e)}")
        return

    # 3. 参数统计方案A
    from torchinfo import summary
    print("\n" + "=" * 60 + "\n方案A: torchinfo\n" + "=" * 60)
    summary(model, input_size=(1, 3, 32, 32), depth=4)

    # 4. 参数统计方案B
    print("\n" + "=" * 60 + "\n方案B: 自定义统计\n" + "=" * 60)
    param_df = detailed_param_report(model)
    print(param_df.head(10).to_markdown())


if __name__ == "__main__":
    main()