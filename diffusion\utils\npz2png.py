import numpy as np
from PIL import Image
import os
import argparse


def save_samples_from_npz(npz_path, output_dir, image_format='png', start_idx=0):
    os.makedirs(output_dir, exist_ok=True)
    data = np.load(npz_path)
    images = data['arr_0']  # 形状应为 [N,H,W] 或 [N,H,W,1]

    # 统一转为 [N,H,W] 形状
    if images.ndim == 4:
        images = images.squeeze(-1)  # [N,H,W,1] -> [N,H,W]
    elif images.ndim == 3 and images.shape[-1] == 1:
        images = images.squeeze(-1)

    labels = data['arr_1'] if 'arr_1' in data else None

    for i in range(len(images)):
        img_array = images[i]
        if img_array.ndim == 3:  # 冗余维度 [1,H,W]
            img_array = img_array.squeeze(0)

        # 确保数据范围和类型正确
        img_array = np.clip(img_array, 0, 255).astype(np.uint8)

        # 创建图像对象
        img = Image.fromarray(img_array)
        if img.mode != 'L':  # 非灰度模式时转换
            img = img.convert('L')  # 'L' 表示8位灰度

        filename = f"{start_idx + i:05d}_class{labels[i]}.{image_format}" if labels else f"{start_idx + i:05d}.{image_format}"
        img.save(os.path.join(output_dir, filename))

    print(f"已保存 {len(images)} 张图片到 {output_dir}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='将扩散模型生成的npz文件转为图片')
    parser.add_argument('--npz', type=str, required=True, help='输入的.npz文件路径')
    parser.add_argument('--o', type=str, default='generated_images1', help='图片输出目录')
    parser.add_argument('--format', type=str, default='png', choices=['png', 'jpg'], help='输出图片格式')
    parser.add_argument('--start_idx', type=int, default=0, help='起始图片编号')

    args = parser.parse_args()

    save_samples_from_npz(
        npz_path=args.npz,
        output_dir=args.o,
        image_format=args.format,
        start_idx=args.start_idx
    )
