import os
import sys
from collections import defaultdict


def count_files_by_directory(root_dir, extensions=None, exclude_dirs=None):
    """
    统计目录树中所有子目录的文件数量

    参数:
        root_dir: 要统计的根目录
        extensions: 要统计的文件扩展名列表(可选)
        exclude_dirs: 要排除的目录名称列表(可选)

    返回:
        sorted_counts: 按文件数量排序的(目录路径, 文件数量)列表
        summary: 包含总计和分布信息的字典
    """
    # 初始化数据结构
    dir_counts = defaultdict(int)
    dir_total_files = 0
    extension_counts = defaultdict(int)

    # 设置默认排除目录
    if exclude_dirs is None:
        exclude_dirs = ['.git', '.idea', '__pycache__', 'node_modules']

    # 遍历目录树
    for root, dirs, files in os.walk(root_dir):
        # 跳过排除目录
        dirs[:] = [d for d in dirs if d not in exclude_dirs]

        # 获取当前目录的文件计数
        if extensions:
            # 按扩展名过滤
            file_list = [f for f in files
                         if os.path.splitext(f)[1].lower() in extensions]
        else:
            file_list = files

        count = len(file_list)
        if count > 0:
            dir_counts[root] = count
            dir_total_files += count

        # 统计扩展名分布
        for file in files:
            _, ext = os.path.splitext(file)
            extension_counts[ext.lower()] += 1

    # 准备排序结果
    sorted_counts = sorted(dir_counts.items(), key=lambda x: (-x[1], x[0]))

    # 计算统计信息
    dir_count = len(dir_counts)
    empty_dirs = sum(1 for root, dirs, files in os.walk(root_dir)
                     if
                     len([f for f in files if extensions is None or os.path.splitext(f)[1].lower() in extensions]) == 0)

    summary = {
        "total_directories_scanned": dir_count,
        "empty_directories_found": empty_dirs,
        "total_files_found": dir_total_files,
        "file_types_distribution": dict(sorted(extension_counts.items(), key=lambda x: -x[1])),
        "largest_directory": sorted_counts[0][0] if sorted_counts else None,
        "largest_directory_count": sorted_counts[0][1] if sorted_counts else 0,
    }

    return sorted_counts, summary


def print_results(counts, summary, limit=None):
    """打印统计结果"""
    print(f"\n{'=' * 50}")
    print(f"{'目录':<60} {'文件数量':>10}")
    print('-' * 71)

    # 打印目录统计
    if limit:
        to_print = counts[:limit] + [('', '...')] + counts[-limit:] if len(counts) > 2 * limit else counts
    else:
        to_print = counts

    for dir_path, count in to_print:
        if dir_path == '':
            print(f"{'...':<60} {count:>10}")
        else:
            display_path = dir_path.replace(os.path.expanduser("~"), "~")  # 缩写家目录
            print(f"{display_path:<60} {count:>10}")

    # 打印摘要统计
    print('=' * 71)
    print("摘要统计:")
    print(f"• 扫描目录总数: {summary['total_directories_scanned']}")
    print(f"• 空目录数量: {summary['empty_directories_found']}")
    print(f"• 文件总数: {summary['total_files_found']}")
    print(f"• 最大的目录: {summary['largest_directory']} (含 {summary['largest_directory_count']} 个文件)")

    # 打印文件类型分布
    print("\n文件类型分布:")
    total_files = summary['total_files_found']
    for ext, count in summary['file_types_distribution'].items():
        percentage = (count / total_files) * 100
        print(f"  {ext if ext else '无扩展名'}: {count:>6} 文件 ({percentage:.1f}%)")


def main():
    # 解析命令行参数
    if len(sys.argv) < 2:
        print("使用方法: python file_counter.py <目录> [扩展名]... [--exclude 排除目录]... [--limit N]")
        print("示例: python file_counter.py ~/projects .py .js --exclude logs --limit 20")
        return

    # 获取目标目录
    target_dir = os.path.expanduser(sys.argv[1])

    # 验证目标目录
    if not os.path.isdir(target_dir):
        print(f"错误: '{target_dir}' 不是一个有效的目录")
        return

    # 解析其他参数
    extensions = set()
    exclude_dirs = []
    limit = None

    i = 2
    while i < len(sys.argv):
        if sys.argv[i] == "--exclude":
            if i + 1 < len(sys.argv):
                exclude_dirs.append(sys.argv[i + 1])
                i += 2
            else:
                print("错误: --exclude 需要指定目录名称")
                return
        elif sys.argv[i] == "--limit":
            if i + 1 < len(sys.argv):
                try:
                    limit = int(sys.argv[i + 1])
                    i += 2
                except ValueError:
                    print("错误: --limit 需要指定整数")
                    return
            else:
                print("错误: --limit 需要指定数字")
                return
        elif sys.argv[i].startswith("."):
            extensions.add(sys.argv[i].lower())
            i += 1
        else:
            print(f"警告: 忽略未知参数 '{sys.argv[i]}'")
            i += 1

    # 进行文件统计
    print(f"\n正在统计目录: {target_dir}")
    if extensions:
        print(f"限制扩展名: {', '.join(sorted(extensions))}")
    if exclude_dirs:
        print(f"排除目录: {', '.join(exclude_dirs)}")

    sorted_counts, summary = count_files_by_directory(
        target_dir,
        extensions=extensions if extensions else None,
        exclude_dirs=exclude_dirs
    )

    # 输出结果
    print_results(sorted_counts, summary, limit=limit)

    # 额外的空间计算
    total_size = 0
    for dir_path, _ in sorted_counts:
        for dirpath, _, filenames in os.walk(dir_path):
            for f in filenames:
                fp = os.path.join(dirpath, f)
                total_size += os.path.getsize(fp)

    print(f"\n所有文件总大小: {total_size / (1024 * 1024):.2f} MB")


if __name__ == "__main__":
    main()