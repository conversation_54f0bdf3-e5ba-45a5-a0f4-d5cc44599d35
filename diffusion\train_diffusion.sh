#!/bin/bash
# 添加当前目录到 Python 路径
export PYTHONPATH="$(pwd):$PYTHONPATH"

# 先获取数据集绝对路径
DATA_DIR="./Filter/"
export CUDA_VISIBLE_DEVICES=1
python ./improved_diffusion/scripts/image_train.py \
    --class_cond True \
    --class_mapping_path './NID_class_mapping.json' \
    --save_dir './CG_100_timestamps' \
    --data_dir "$DATA_DIR" \
    --image_size 32 \
    --num_channels 128 \
    --num_res_blocks 2 \
    --diffusion_steps 100 \
    --noise_schedule linear \
    --lr 1e-4 \
    --batch_size 32