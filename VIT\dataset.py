from torchvision import datasets
from torch.utils.data import Dataset
import os


class DefaultDataset(Dataset):
    """
    Default数据集类，继承自torch.utils.data.Dataset
    能够处理ISAC类型的数据
    """
    def __init__(self,**kwargs):
        """
        初始化数据集
        :param root_dir: 数据集根目录
        :param transform: 图像转换操作
        """
        root_dir = kwargs.get('root', '')
        self.transform = kwargs.get('transform', None)
        if root_dir is None:
            raise ValueError("root_dir must be provided in kwargs")
        if not os.path.exists(root_dir):
            raise FileNotFoundError(f"Root directory {root_dir} does not exist")
        self.image_paths = []
        self.labels = []
        self.class_idx = {name : i for i, name in enumerate(sorted(os.listdir(root_dir)))}
        # 遍历根目录下的所有子目录和文件：只有根目录下一级子目录是类别名，类别目录下所有可能的文件都是图像文件
        for class_name in os.listdir(root_dir):
            class_dir = os.path.join(root_dir, class_name)
            if os.path.isdir(class_dir):
                # 递归遍历class_dir下所有文件
                for root, _, files in os.walk(class_dir):
                    for filename in files:
                        if filename.endswith('.jpg') or filename.endswith('.png'):
                            self.image_paths.append(os.path.join(root, filename))
                            self.labels.append(self.class_idx[class_name])

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img_path = self.image_paths[idx]
        label = self.labels[idx]
        from PIL import Image
        image = Image.open(img_path).convert('RGB')
        if self.transform:
            image = self.transform(image)
        return image, label


class ValDataset(Dataset):
    """
    Default数据集类，继承自torch.utils.data.Dataset
    能够处理ISAC类型的数据
    """
    def __init__(self,**kwargs):
        """
        初始化数据集
        :param root_dir: 数据集根目录
        :param transform: 图像转换操作
        """
        root_dir = kwargs.get('root', '')

        self.transform = kwargs.get('transform', None)
        if root_dir is None:
            raise ValueError("root_dir must be provided in kwargs")
        if not os.path.exists(root_dir):
            raise FileNotFoundError(f"Root directory {root_dir} does not exist")
        self.image_paths = []
        self.labels = []
        self.class_idx = {name : i for i, name in enumerate(sorted(os.listdir(root_dir)))}
        # 遍历根目录下的所有子目录和文件：只有根目录下一级子目录是类别名，类别目录下所有可能的文件都是图像文件
        for class_name in os.listdir(root_dir):
            class_dir = os.path.join(root_dir, class_name)
            if os.path.isdir(class_dir):
                # 递归遍历class_dir下所有文件
                for root, _, files in os.walk(class_dir):
                    cnt = 0
                    for filename in files:
                        cnt += 1
                        if cnt > 10:
                            break
                        if filename.endswith('.jpg') or filename.endswith('.png'):
                            self.image_paths.append(os.path.join(root, filename))
                            self.labels.append(self.class_idx[class_name])

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img_path = self.image_paths[idx]
        label = self.labels[idx]
        from PIL import Image
        image = Image.open(img_path).convert('RGB')
        if self.transform:
            image = self.transform(image)
        return image, label



if __name__ == '__main__':
    import torchvision
    train = torchvision.datasets.ImageFolder(
        root='../data/test_dataset',
        transform=None
    )
    for i, (img, label) in enumerate(train):
        print(i, img, label)

