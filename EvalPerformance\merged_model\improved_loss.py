import torch
import torch.nn as nn
import torch.nn.functional as F


class MultiTaskLoss(nn.Module):
    """
    多任务损失函数，包含：
    - 序列分支损失
    - 图像分支损失
    - 融合分支损失
    - 可学习的任务权重
    - 可选的一致性正则化
    """

    def __init__(self, num_tasks=3, consistency_weight=0.1, temperature=0.1):
        """
        :param num_tasks: 任务数量 (默认3: 序列, 图像, 融合)
        :param consistency_weight: 特征一致性正则化权重
        :param temperature: 特征一致性计算中的温度参数
        """
        super().__init__()
        # 可学习的任务权重
        self.alpha = nn.Parameter(torch.ones(num_tasks))
        self.consistency_weight = consistency_weight
        self.temperature = temperature
        self.ce_loss = nn.CrossEntropyLoss()

        # 初始化权重
        nn.init.constant_(self.alpha, 1.0 / num_tasks)

    def forward(self, outputs, targets):
        """
        计算多任务损失
        :param outputs: 模型输出字典，包含:
            'seq_out': 序列分支预测 [B, C]
            'img_out': 图像分支预测 [B, C]
            'fusion_out': 融合分支预测 [B, C]
            'features': 特征字典，包含 'seq', 'img', 'fused' 特征
        :param targets: 真实标签 [B]
        :return: 总损失, 损失字典
        """
        # 基本分类损失
        seq_loss = self.ce_loss(outputs['seq_out'], targets)
        img_loss = self.ce_loss(outputs['img_out'], targets)
        fusion_loss = self.ce_loss(outputs['output'], targets)


        # 归一化任务权重
        weights = F.softmax(self.alpha, dim=0)

        # 加权总损失
        total_loss = (
                weights[0] * seq_loss +
                weights[1] * img_loss +
                weights[2] * fusion_loss
        )
        # 返回损失字典
        loss_dict = {
            'total_loss': total_loss.item(),
            'seq_loss': seq_loss.item(),
            'img_loss': img_loss.item(),
            'fusion_loss': fusion_loss.item(),
            'weights': weights.detach().cpu().numpy().tolist()
        }

        return total_loss

