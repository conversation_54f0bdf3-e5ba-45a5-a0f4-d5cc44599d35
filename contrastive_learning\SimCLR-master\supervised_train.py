import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import datasets, transforms, models
from torch.utils.data import DataLoader
import os
import yaml
import argparse
from tqdm import tqdm  # 添加tqdm进度条支持
from torch.utils.tensorboard import SummaryWriter  # 添加TensorBoard支持
import time
import datetime


def parse_args():
    parser = argparse.ArgumentParser(description='Finetuning with YAML config')
    parser.add_argument('-c','--config', type=str, required=True,
                        help='Path to YAML configuration file')
    return parser.parse_args()


def load_config(config_path):
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


# 解析命令行参数
args = parse_args()

# 加载YAML配置
config = load_config(args.config)

# 设置GPU设备
if 'CUDA_VISIBLE_DEVICES' in config:
    os.environ['CUDA_VISIBLE_DEVICES'] = config['CUDA_VISIBLE_DEVICES']

# 使用配置中的参数
train_data_path = config['train_data_path']
val_data_path = config['val_data_path']
pretrained_model_path = config['pretrained_model_path']
img_size = config['img_size']
batch_size = config['batch_size']
learning_rate = config['learning_rate']
freeze_train = config['freeze_train']
finetune_epochs = config['finetune_epochs']
freeze_epoch = config['freeze_epoch']
base_model = config['base_model']
out_dim = config['out_dim']
transform_mean = config['transform']['mean']
transform_std = config['transform']['std']
save_dir = config['save_dir']
save_name = config['save_name']
log_name = config['log_name']
# 创建保存目录
os.makedirs(save_dir, exist_ok=True)

# 设置TensorBoard日志目录
log_dir = os.path.join(save_dir, "logs",datetime.datetime.now().strftime('%Y_%m_%d'),log_name)
os.makedirs(log_dir, exist_ok=True)
writer = SummaryWriter(log_dir=log_dir)  # 创建TensorBoard写入器

# 数据转换
train_transform = transforms.Compose([
    transforms.Resize((img_size, img_size)),
    transforms.ToTensor(),
    transforms.Normalize(mean=transform_mean, std=transform_std)
])

val_transform = transforms.Compose([
    transforms.Resize((img_size, img_size)),
    transforms.ToTensor(),
    transforms.Normalize(mean=transform_mean, std=transform_std)
])

# 加载数据集
train_dataset = datasets.ImageFolder(str(train_data_path), transform=train_transform)
val_dataset = datasets.ImageFolder(str(val_data_path), transform=val_transform)
num_classes = len(train_dataset.classes)

# 创建数据加载器
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=4, pin_memory=True)
val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=4, pin_memory=True)


class ResNetSimCLR(nn.Module):
    def __init__(self, base_model, out_dim, num_classes):
        super(ResNetSimCLR, self).__init__()
        self.resnet_dict = {
            "resnet18": models.resnet18(pretrained=False, num_classes=out_dim),
            "resnet50": models.resnet50(pretrained=False, num_classes=out_dim)
        }
        self.backbone = self._get_basemodel(base_model)
        fc_in_features = self.backbone.fc.in_features
        self.backbone.fc = nn.Identity ()
        self.classifier = nn.Linear(fc_in_features, num_classes)
    def _get_basemodel(self, model_name):
        model = self.resnet_dict.get(model_name)
        if model is None:
            raise ValueError(f"Invalid backbone: {model_name}. Choose from {list(self.resnet_dict.keys())}")
        return model
    def freeze_backbone(self):
        for param in self.backbone.parameters():
            param.requires_grad = False
        for param in self.classifier.parameters():
            param.requires_grad = True
        print("Backbone frozen")

    def unfreeze_backbone(self):
        for param in self.backbone.parameters():
            param.requires_grad = True
        for param in self.classifier.parameters():
            param.requires_grad = True
        print("Backbone unfrozen")

    def forward(self, x):
        x = self.backbone(x)
        x = self.classifier(x)
        return x


def validate(model, loader, device, epoch, writer=None):
    model.eval()
    correct = 0
    total = 0
    val_loss = 0.0
    criterion = nn.CrossEntropyLoss()
    passbar = tqdm(loader, desc=f'Validation Epoch {epoch + 1}', leave=False)
    with torch.no_grad():
        for images, labels in passbar:
            images, labels = images.to(device), labels.to(device)
            outputs = model(images)
            loss = criterion(outputs, labels)
            val_loss += loss.item() * images.size(0)
            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()

    val_acc = 100 * correct / total
    val_loss = val_loss / len(loader.dataset)

    # 记录验证结果到TensorBoard
    if writer:
        writer.add_scalar('Loss/val', val_loss, epoch)
        writer.add_scalar('Accuracy/val', val_acc, epoch)

    return val_acc, val_loss


def load_pretrained_weights(model, pretrained_path):
    if pretrained_path is None:
        print("No pretrained weights found, training from scratch.")
        return model
    checkpoint = torch.load(pretrained_path, map_location='cpu')

    # 处理不同格式的checkpoint
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    elif 'state_dict' in checkpoint:
        state_dict = checkpoint['state_dict']
    elif 'model' in checkpoint:
        state_dict = checkpoint['model']
    else:
        state_dict = checkpoint

    # 移除可能存在的module.前缀 (如果是DataParallel保存的)
    state_dict = {k.replace('encoder.', ''): v for k, v in state_dict.items()}
    state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
    # 不加载分类头的权重
    filtered_state_dict = {k: v for k, v in state_dict.items() if not k.startswith('classifier')}
    # 严格加载匹配的参数
    msg = model.load_state_dict(filtered_state_dict, strict=False)
    print(f'Missing keys: {msg.missing_keys}')
    print(f'Unexpected keys: {msg.unexpected_keys}')
    return model


if __name__ == '__main__':
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    # 使用配置参数初始化模型
    model = ResNetSimCLR(base_model, out_dim, num_classes)
    model = model.to(device)
    # 加载预训练权重
    model = load_pretrained_weights(model, pretrained_model_path)

    if freeze_train:
        model.freeze_backbone()
        # 初始只优化分类器
        optimizer = optim.Adam(model.classifier.parameters(), lr=learning_rate)
    else:
        # 如果不冻结，优化所有参数
        optimizer = optim.Adam(model.parameters(), lr=learning_rate)

    # 添加学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=len(train_loader), eta_min=0,
                                                           last_epoch=-1)

    criterion = nn.CrossEntropyLoss()
    best_acc = 0.0

    print("Starting training...")
    print(f"TensorBoard logs saved to: {log_dir}")

    # 训练循环
    for epoch in range(finetune_epochs):
        start_time = time.time()

        # 解冻逻辑
        if freeze_train and epoch == freeze_epoch:
            model.unfreeze_backbone()
            # 解冻后需要更新优化器以包含所有参数
            optimizer = optim.Adam(model.parameters(), lr=learning_rate)
            print("Optimizer reset for full model training")

        model.train()
        running_loss = 0.0

        # 使用tqdm封装训练过程
        train_bar = tqdm(train_loader, desc=f'Epoch {epoch + 1}/{finetune_epochs}', leave=False)
        for images, labels in train_bar:
            images, labels = images.to(device), labels.to(device)

            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()

            running_loss += loss.item() * images.size(0)
            # 更新进度条显示当前loss
            train_bar.set_postfix(loss=loss.item())

        # 计算平均训练损失
        epoch_loss = running_loss / len(train_loader.dataset)

        # 记录训练损失到TensorBoard
        writer.add_scalar('Loss/train', epoch_loss, epoch)

        # 验证
        val_acc, val_loss = validate(model, val_loader, device, epoch, writer)

        # 更新学习率
        scheduler.step(val_acc)

        # 记录当前学习率
        current_lr = optimizer.param_groups[0]['lr']
        writer.add_scalar('Learning Rate', current_lr, epoch)

        epoch_time = time.time() - start_time

        print(f'Epoch [{epoch + 1}/{finetune_epochs}] '
              f'Time: {epoch_time:.1f}s '
              f'Loss: {epoch_loss:.4f} '
              f'Val Loss: {val_loss:.4f} '
              f'Val Acc: {val_acc:.2f}% '
              f'LR: {current_lr:.6f}')

        # 保存最佳模型
        if val_acc > best_acc:
            best_acc = val_acc
            save_model_path = os.path.join(save_dir, save_name)
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'config': config
            }, str(save_model_path))
            print(f"New best model saved with accuracy: {best_acc:.2f}% at {save_model_path}")

    # 关闭TensorBoard写入器
    writer.close()
    print(f"Training complete. Best validation accuracy: {best_acc:.2f}%")
    print(f"TensorBoard logs saved to: {log_dir}")
    print(f"To view results, run: tensorboard --logdir={log_dir}")