import torch
import torch.nn as nn
from transformers import <PERSON><PERSON><PERSON><PERSON><PERSON>, BertPreTrainedModel, BertModel
import torch.nn.functional as F
import math
class MultiScaleConv1D(nn.Module):
    """Multi-scale 1D convolution module"""

    def __init__(self, input_dim, output_channels):
        super().__init__()

        # Three branches with different scales, ensuring correct total channels
        channels_per_branch = output_channels // 3
        remaining_channels = output_channels - 2 * channels_per_branch

        self.local_conv = nn.Conv1d(1, channels_per_branch, kernel_size=4, stride=4, padding=0)
        self.medium_conv = nn.Conv1d(1, channels_per_branch, kernel_size=8, stride=8, padding=0)
        self.global_conv = nn.Conv1d(1, remaining_channels, kernel_size=16, stride=16, padding=0)

        # Batch normalization
        self.local_bn = nn.BatchNorm1d(channels_per_branch)
        self.medium_bn = nn.BatchNorm1d(channels_per_branch)
        self.global_bn = nn.BatchNorm1d(remaining_channels)

        # Activation function
        self.activation = nn.GELU()

        # Feature alignment layers (align sequences of different lengths to same length)
        self.align_local = nn.AdaptiveAvgPool1d(64)  # 1024/16 = 64
        self.align_medium = nn.AdaptiveAvgPool1d(64)

    def forward(self, x):
        # x: [batch_size, 1, input_dim]

        # Local features (kernel=4, stride=4) -> [batch_size, channels_per_branch, 256]
        local_feat = self.activation(self.local_bn(self.local_conv(x)))
        local_feat = self.align_local(local_feat)  # -> [batch_size, channels_per_branch, 64]

        # Medium-scale features (kernel=8, stride=8) -> [batch_size, channels_per_branch, 128]
        medium_feat = self.activation(self.medium_bn(self.medium_conv(x)))
        medium_feat = self.align_medium(medium_feat)  # -> [batch_size, channels_per_branch, 64]

        # Global features (kernel=16, stride=16) -> [batch_size, remaining_channels, 64]
        global_feat = self.activation(self.global_bn(self.global_conv(x)))

        # Concatenate multi-scale features
        multi_scale_feat = torch.cat([local_feat, medium_feat, global_feat], dim=1)
        # -> [batch_size, output_channels, 64]

        return multi_scale_feat


class LightweightAttention(nn.Module):
    """Lightweight self-attention module"""

    def __init__(self, channels, reduction=8):
        super().__init__()
        self.channels = channels
        self.reduction = reduction

        # Query, key, value projections (dimensionality reduction to decrease computation)
        self.query = nn.Linear(channels, channels // reduction)
        self.key = nn.Linear(channels, channels // reduction)
        self.value = nn.Linear(channels, channels)

        # Output projection
        self.out_proj = nn.Linear(channels, channels)
        self.dropout = nn.Dropout(0.1)

        # Layer normalization
        self.layer_norm = nn.LayerNorm(channels)

    def forward(self, x):
        # x: [batch_size, channels, seq_len]
        batch_size, channels, seq_len = x.shape

        # Transpose to [batch_size, seq_len, channels]
        x_t = x.permute(0, 2, 1)

        # Input for residual connection
        residual = x_t

        # Compute attention
        q = self.query(x_t)  # [batch_size, seq_len, channels//reduction]
        k = self.key(x_t)    # [batch_size, seq_len, channels//reduction]
        v = self.value(x_t)  # [batch_size, seq_len, channels]

        # Attention scores
        attn_scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(channels // self.reduction)
        attn_weights = F.softmax(attn_scores, dim=-1)
        attn_weights = self.dropout(attn_weights)

        # Apply attention
        attn_output = torch.matmul(attn_weights, v)

        # Output projection
        output = self.out_proj(attn_output)

        # Residual connection and layer normalization
        output = self.layer_norm(residual + output)

        # Transpose back to [batch_size, channels, seq_len]
        return output.permute(0, 2, 1)


class AdvancedSequenceEncoder(nn.Module):
    """Improved sequence encoder to replace simple 1D convolution"""

    def __init__(self, input_dim=1024, output_channels=256):
        super().__init__()

        # Multi-scale convolution feature extraction
        self.multi_scale_conv = MultiScaleConv1D(input_dim, output_channels)

        # Lightweight attention module
        self.attention = LightweightAttention(output_channels)

        # Additional feature enhancement layers
        self.feature_enhance = nn.Sequential(
            nn.Conv1d(output_channels, output_channels, kernel_size=3, padding=1),
            nn.BatchNorm1d(output_channels),
            nn.GELU(),
            nn.Dropout(0.1)
        )

        # Final feature fusion
        self.final_conv = nn.Conv1d(output_channels, output_channels, kernel_size=1)

    def forward(self, x):
        # x: [batch_size, 1, input_dim]

        # Multi-scale feature extraction
        multi_scale_feat = self.multi_scale_conv(x)

        # Attention enhancement
        attn_feat = self.attention(multi_scale_feat)

        # Feature enhancement
        enhanced_feat = self.feature_enhance(attn_feat)

        # Residual connection
        output = self.final_conv(enhanced_feat + attn_feat)

        return output
class BertEncoderConfig(BertConfig):
    def __init__(self,
                 input_dim=1024,
                 conv_kernel_size=16,
                 conv_output_channels=256,
                 out_dim=128,
                 num_hidden_layers=16,
                 hidden_dropout_prob=0.1,
                 attention_probs_dropout_prob=0.1,
                 hidden_act="gelu",
                 intermediate_size=3072,
                 layer_norm_eps=1e-12,
                 max_position_embeddings=512,
                 num_attention_heads=12,
                 initializer_range=0.02,
                 pad_token_id=0,
                 position_embedding_type="absolute",
                 type_vocab_size=2,
                 use_cache=True,
                 vocab_size=30522,
                 **kwargs):
        super().__init__(
            hidden_dropout_prob=hidden_dropout_prob,
            attention_probs_dropout_prob=attention_probs_dropout_prob,
            hidden_act=hidden_act,
            intermediate_size=intermediate_size,
            layer_norm_eps=layer_norm_eps,
            max_position_embeddings=max_position_embeddings,
            num_attention_heads=num_attention_heads,
            initializer_range=initializer_range,
            pad_token_id=pad_token_id,
            position_embedding_type=position_embedding_type,
            type_vocab_size=type_vocab_size,
            use_cache=use_cache,
            vocab_size=vocab_size,
            **kwargs
        )
        self.input_dim = input_dim
        self.conv_kernel_size = conv_kernel_size
        self.conv_output_channels = conv_output_channels
        self.out_dim = out_dim
        self.hidden_size = conv_output_channels
        self.num_hidden_layers = num_hidden_layers
        self.output_sequence_length = input_dim // conv_kernel_size
        # CLS token会增加序列长度
        self.total_sequence_length = self.output_sequence_length + 1


class BertEncoder(BertPreTrainedModel):
    """基于BERT的字节序列编码器模型"""

    def __init__(self, config):
        super().__init__(config)
        self.config = config

        # 输入卷积层
        self.advanced_seq_scale_encoder = AdvancedSequenceEncoder(
            input_dim=config.input_dim,
            output_channels=config.conv_output_channels
        )

        # CLS token嵌入 (可学习)
        self.cls_token = nn.Parameter(torch.randn(1, 1, config.hidden_size))

        # BERT模型
        self.bert = BertModel(self._create_bert_config())

        # 最终投影层
        self.final_projection = nn.Linear(config.hidden_size, config.out_dim)

        self.post_init()

    def _create_bert_config(self):
        """创建适配的BERT配置"""
        config = BertConfig(
            vocab_size=1,
            hidden_size=self.config.conv_output_channels,
            num_hidden_layers=self.config.num_hidden_layers,
            num_attention_heads=8,
            intermediate_size=1024,
            max_position_embeddings=self.config.total_sequence_length,
            hidden_act="gelu",
            position_embedding_type="absolute",
            use_cache=False,
            output_hidden_states=True,
        )
        return config

    def forward(self, input_bytes, attention_mask=None):
        # 添加通道维度
        x = input_bytes.unsqueeze(1)

        # 应用卷积和激活
        conv_output = self.advanced_seq_scale_encoder(x)

        # 转置维度: [batch, channels, seq_len] -> [batch, seq_len, channels]
        conv_output = conv_output.permute(0, 2, 1)

        batch_size = conv_output.shape[0]

        # 添加CLS token到序列开头
        cls_tokens = self.cls_token.expand(batch_size, -1, -1)
        conv_output_with_cls = torch.cat((cls_tokens, conv_output), dim=1)

        # 创建注意力掩码 (包含CLS token)
        if attention_mask is None:
            bert_attention_mask = torch.ones(
                (batch_size, self.config.total_sequence_length),
                device=conv_output_with_cls.device
            )
        else:
            # 在开头添加CLS的mask
            cls_mask = torch.ones(
                (batch_size, 1),
                dtype=attention_mask.dtype,
                device=attention_mask.device
            )
            bert_attention_mask = torch.cat([cls_mask, attention_mask], dim=1)

        # BERT处理
        bert_outputs = self.bert(
            inputs_embeds=conv_output_with_cls,
            attention_mask=bert_attention_mask
        )

        # 获取CLS token的输出 (序列的第一个位置)
        cls_output = bert_outputs.last_hidden_state[:, 0, :]

        # 最终投影
        feature_vector = self.final_projection(cls_output)

        return feature_vector


if __name__ == '__main__':
    # 创建配置
    config = BertEncoderConfig(
        input_dim=1024,
        conv_kernel_size=16,
        conv_output_channels=32,
        out_dim=128,
        num_hidden_layers=6,
        intermediate_size=256
    )

    # 创建编码器
    encoder = BertEncoder(config)

    # 测试输入
    input_bytes = torch.randint(0, 256, (32, 1024)).float()
    attention_mask = torch.ones(32, 64)  # 1024/16=64

    print("=== 模型基本信息 ===")
    print(f"模型类型: {type(encoder).__name__}")
    print(f"输入形状: {input_bytes.shape}")
    print(f"注意力掩码形状: {attention_mask.shape}")

    # 前向传播
    output = encoder(input_bytes, attention_mask)
    print(f"输出形状: {output.shape}")

    # 验证中间形状
    conv_output = encoder.conv(input_bytes.unsqueeze(1))
    conv_output = conv_output.permute(0, 2, 1)
    print(f"卷积后形状: {conv_output.shape}")

    # 添加CLS token后
    cls_token = encoder.cls_token
    print(f"CLS token形状: {cls_token.shape}")

    # 最终BERT输入形状
    cls_output = cls_token.expand(32, -1, -1)
    bert_input = torch.cat((cls_output, conv_output), dim=1)
    print(f"BERT输入形状: {bert_input.shape}")
    print(f"BERT输出形状: {output.shape}")