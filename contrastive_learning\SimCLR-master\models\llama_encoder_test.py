import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import LlamaConfig, LlamaPreTrainedModel, LlamaModel
import math


class MultiScaleConv1D(nn.Module):
    """Multi-scale 1D convolution module"""

    def __init__(self, input_dim, output_channels):
        super().__init__()

        # Three branches with different scales, ensuring correct total channels
        channels_per_branch = output_channels // 3
        remaining_channels = output_channels - 2 * channels_per_branch

        self.local_conv = nn.Conv1d(1, channels_per_branch, kernel_size=4, stride=4, padding=0)
        self.medium_conv = nn.Conv1d(1, channels_per_branch, kernel_size=8, stride=8, padding=0)
        self.global_conv = nn.Conv1d(1, remaining_channels, kernel_size=16, stride=16, padding=0)

        # Batch normalization
        self.local_bn = nn.BatchNorm1d(channels_per_branch)
        self.medium_bn = nn.BatchNorm1d(channels_per_branch)
        self.global_bn = nn.BatchNorm1d(remaining_channels)

        # Activation function
        self.activation = nn.GELU()

        # Feature alignment layers (align sequences of different lengths to same length)
        self.align_local = nn.AdaptiveAvgPool1d(64)  # 1024/16 = 64
        self.align_medium = nn.AdaptiveAvgPool1d(64)

    def forward(self, x):
        # x: [batch_size, 1, input_dim]

        # Local features (kernel=4, stride=4) -> [batch_size, channels_per_branch, 256]
        local_feat = self.activation(self.local_bn(self.local_conv(x)))
        local_feat = self.align_local(local_feat)  # -> [batch_size, channels_per_branch, 64]

        # Medium-scale features (kernel=8, stride=8) -> [batch_size, channels_per_branch, 128]
        medium_feat = self.activation(self.medium_bn(self.medium_conv(x)))
        medium_feat = self.align_medium(medium_feat)  # -> [batch_size, channels_per_branch, 64]

        # Global features (kernel=16, stride=16) -> [batch_size, remaining_channels, 64]
        global_feat = self.activation(self.global_bn(self.global_conv(x)))

        # Concatenate multi-scale features
        multi_scale_feat = torch.cat([local_feat, medium_feat, global_feat], dim=1)
        # -> [batch_size, output_channels, 64]

        return multi_scale_feat


class LightweightAttention(nn.Module):
    """Lightweight self-attention module"""

    def __init__(self, channels, reduction=8):
        super().__init__()
        self.channels = channels
        self.reduction = reduction

        # Query, key, value projections (dimensionality reduction to decrease computation)
        self.query = nn.Linear(channels, channels // reduction)
        self.key = nn.Linear(channels, channels // reduction)
        self.value = nn.Linear(channels, channels)

        # Output projection
        self.out_proj = nn.Linear(channels, channels)
        self.dropout = nn.Dropout(0.1)

        # Layer normalization
        self.layer_norm = nn.LayerNorm(channels)

    def forward(self, x):
        # x: [batch_size, channels, seq_len]
        batch_size, channels, seq_len = x.shape

        # Transpose to [batch_size, seq_len, channels]
        x_t = x.permute(0, 2, 1)

        # Input for residual connection
        residual = x_t

        # Compute attention
        q = self.query(x_t)  # [batch_size, seq_len, channels//reduction]
        k = self.key(x_t)    # [batch_size, seq_len, channels//reduction]
        v = self.value(x_t)  # [batch_size, seq_len, channels]

        # Attention scores
        attn_scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(channels // self.reduction)
        attn_weights = F.softmax(attn_scores, dim=-1)
        attn_weights = self.dropout(attn_weights)

        # Apply attention
        attn_output = torch.matmul(attn_weights, v)

        # Output projection
        output = self.out_proj(attn_output)

        # Residual connection and layer normalization
        output = self.layer_norm(residual + output)

        # Transpose back to [batch_size, channels, seq_len]
        return output.permute(0, 2, 1)


class AdvancedSequenceEncoder(nn.Module):
    """Improved sequence encoder to replace simple 1D convolution"""

    def __init__(self, input_dim=1024, output_channels=256):
        super().__init__()

        # Multi-scale convolution feature extraction
        self.multi_scale_conv = MultiScaleConv1D(input_dim, output_channels)

        # Lightweight attention module
        self.attention = LightweightAttention(output_channels)

        # Additional feature enhancement layers
        self.feature_enhance = nn.Sequential(
            nn.Conv1d(output_channels, output_channels, kernel_size=3, padding=1),
            nn.BatchNorm1d(output_channels),
            nn.GELU(),
            nn.Dropout(0.1)
        )

        # Final feature fusion
        self.final_conv = nn.Conv1d(output_channels, output_channels, kernel_size=1)

    def forward(self, x):
        # x: [batch_size, 1, input_dim]

        # Multi-scale feature extraction
        multi_scale_feat = self.multi_scale_conv(x)

        # Attention enhancement
        attn_feat = self.attention(multi_scale_feat)

        # Feature enhancement
        enhanced_feat = self.feature_enhance(attn_feat)

        # Residual connection
        output = self.final_conv(enhanced_feat + attn_feat)

        return output


class LlamaEncoderConfig(LlamaConfig):
    def __init__(self,
                 input_dim=1024,
                 conv_kernel_size=16,  # Convolution kernel size = stride
                 conv_output_channels=256,  # Convolution output channels
                 out_dim=128,  # Added output dimension parameter
                 **kwargs):
        super().__init__(**kwargs)
        # Custom parameters
        self.conv_kernel_size = conv_kernel_size
        self.conv_output_channels = conv_output_channels
        self.out_dim = out_dim  # Output dimension
        self.hidden_size = conv_output_channels  # hidden_size should match conv output channels
        # Calculate sequence length after convolution
        self.output_sequence_length = input_dim // conv_kernel_size


class LlamaEncoder(LlamaPreTrainedModel):
    """LLaMA-based byte sequence encoder model

    Input: [batch_size, input_dim] normalized byte stream
    Output: [batch_size, out_dim] feature vector
    """

    def __init__(self, config):
        super().__init__(config)
        self.config = config

        # Use improved sequence encoder to replace simple 1D convolution
        self.advanced_encoder = AdvancedSequenceEncoder(
            input_dim=1024,  # Fixed input dimension
            output_channels=config.conv_output_channels
        )
        # Create LLaMA model with custom initialization
        self.llama = LlamaModel(self._create_llama_config())

        # Global pooling layer (compresses sequence dimension to 1)
        self.global_pool = nn.AdaptiveAvgPool1d(1)

        # Final projection layer (maps feature dimension to target output dimension)
        self.final_projection = nn.Linear(config.conv_output_channels, config.out_dim)

        # Initialize weights
        self.post_init()

    def _create_llama_config(self):
        """Create adapted LLaMA configuration"""
        config = LlamaConfig(
            vocab_size=1,  # No vocabulary used
            hidden_size=self.config.conv_output_channels,  # Matches conv output channels
            intermediate_size=getattr(self.config, 'intermediate_size', 1024),
            num_hidden_layers=getattr(self.config, 'num_hidden_layers', 6),
            num_attention_heads=getattr(self.config, 'num_attention_heads', 8),
            num_key_value_heads=getattr(self.config, 'num_key_value_heads', 4),
            max_position_embeddings=getattr(self.config, 'max_position_embeddings', 64),
            rms_norm_eps=getattr(self.config, 'rms_norm_eps', 1e-6),
            use_cache=False,  # Don't use cache during training
        )
        return config

    def forward(self, input_bytes, attention_mask=None):
        """
        Forward pass
        :param input_bytes: Input byte stream [batch_size, input_dim]
        :param attention_mask: Attention mask [batch_size, output_sequence_length]
        :return: Feature vector [batch_size, out_dim]
        """
        # Add channel dimension [batch_size, input_dim] -> [batch_size, 1, input_dim]
        x = input_bytes.unsqueeze(1)

        # Feature extraction with improved encoder [batch_size, 1, input_dim] -> [batch_size, conv_output_channels, output_sequence_length]
        conv_output = self.advanced_encoder(x)
        # Transpose dimensions for LLaMA input [batch_size, conv_output_channels, output_sequence_length]
        # -> [batch_size, output_sequence_length, conv_output_channels]
        conv_output = conv_output.permute(0, 2, 1)

        # Create all-ones mask if no attention mask provided
        if attention_mask is None:
            attention_mask = torch.ones(
                conv_output.shape[:2],
                dtype=torch.long,
                device=conv_output.device
            )

        # LLaMA processing
        llama_outputs = self.llama(
            inputs_embeds=conv_output,
            attention_mask=attention_mask
        )

        # Get last hidden state [batch_size, output_sequence_length, conv_output_channels]
        sequence_output = llama_outputs.last_hidden_state

        # Transpose back to conv output format [batch_size, output_sequence_length, conv_output_channels]
        # -> [batch_size, conv_output_channels, output_sequence_length]
        sequence_output = sequence_output.permute(0, 2, 1)

        # Global average pooling, compressing sequence dimension to 1 [batch_size, conv_output_channels, output_sequence_length]
        # -> [batch_size, conv_output_channels, 1]
        pooled_output = self.global_pool(sequence_output)

        # Remove sequence dimension [batch_size, conv_output_channels, 1] -> [batch_size, conv_output_channels]
        pooled_output = pooled_output.squeeze(-1)

        # Final projection to target dimension [batch_size, conv_output_channels] -> [batch_size, out_dim]
        feature_vector = self.final_projection(pooled_output)

        return feature_vector


if __name__ == '__main__':
    # Create configuration
    config = LlamaEncoderConfig(
        input_dim=1024,
        conv_kernel_size=16,
        conv_output_channels=256,  # Increased channels to match attention module
        out_dim=128,
        num_hidden_layers=6,
        num_attention_heads=8,
        intermediate_size=256
    )

    # Create a LLaMA encoder
    encoder = LlamaEncoder(config)
    torch.save(encoder, 'llama_encoder.pth')
    # Create example byte stream (needs to be float type)
    input_bytes = torch.randint(0, 256, (32, 1024)).float()

    # Create example attention mask
    attention_mask = torch.ones(32, 64)  # batch_size=32, seq_len=64

    print("=== Basic Model Information ===")
    print(f"Model type: {type(encoder).__name__}")
    print(f"Input shape: {input_bytes.shape}")
    print(f"Attention mask shape: {attention_mask.shape}")

    # Forward pass
    output = encoder(input_bytes, attention_mask)
    print(f"Output shape: {output.shape}")

    print("\n=== Model Architecture ===")
    print(encoder)

    print("\n=== Model Parameter Statistics ===")
    total_params = 0
    trainable_params = 0

    for name, parameter in encoder.named_parameters():
        param_count = parameter.numel()
        total_params += param_count
        if parameter.requires_grad:
            trainable_params += param_count
        print(f"{name}: {param_count:,} parameters")

    print(f"\nTotal parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")
    print(f"Non-trainable parameters: {total_params - trainable_params:,}")

    print("\n=== Module-wise Parameter Count ===")
    # Improved encoder parameters
    encoder_params = sum(p.numel() for p in encoder.advanced_encoder.parameters())
    print(f"Improved encoder parameters: {encoder_params:,}")

    # LLaMA model parameters
    llama_params = sum(p.numel() for p in encoder.llama.parameters())
    print(f"LLaMA model parameters: {llama_params:,}")

    # Final projection layer parameters
    final_proj_params = sum(p.numel() for p in encoder.final_projection.parameters())
    print(f"Final projection layer parameters: {final_proj_params:,}")

    # Global pooling layer parameters
    global_pool_params = sum(p.numel() for p in encoder.global_pool.parameters())
    print(f"Global pooling layer parameters: {global_pool_params:,}")

    # Detailed encoder module parameter statistics
    print("\n=== Detailed Improved Encoder Parameters ===")
    multi_scale_params = sum(p.numel() for p in encoder.advanced_encoder.multi_scale_conv.parameters())
    attention_params = sum(p.numel() for p in encoder.advanced_encoder.attention.parameters())
    enhance_params = sum(p.numel() for p in encoder.advanced_encoder.feature_enhance.parameters())
    final_conv_params = sum(p.numel() for p in encoder.advanced_encoder.final_conv.parameters())

    print(f"Multi-scale convolution module: {multi_scale_params:,}")
    print(f"Attention module: {attention_params:,}")
    print(f"Feature enhancement module: {enhance_params:,}")
    print(f"Final convolution layer: {final_conv_params:,}")

    print("\n=== Memory Estimation ===")
    # Estimate model memory usage (assuming float32, 4 bytes per parameter)
    memory_mb = total_params * 4 / (1024 * 1024)
    print(f"Model parameter memory usage: {memory_mb:.2f} MB")

    # Activation memory estimation during forward pass
    input_memory_mb = input_bytes.numel() * 4 / (1024 * 1024)
    output_memory_mb = output.numel() * 4 / (1024 * 1024)
    print(f"Input data memory usage: {input_memory_mb:.2f} MB")
    print(f"Output data memory usage: {output_memory_mb:.2f} MB")

    print("\n=== LLaMA Configuration Details ===")
    llama_config = encoder._create_llama_config()
    print(f"Hidden dimension: {llama_config.hidden_size}")
    print(f"Number of attention heads: {llama_config.num_attention_heads}")
    print(f"Number of key-value heads: {llama_config.num_key_value_heads}")
    print(f"Number of Transformer layers: {llama_config.num_hidden_layers}")
    print(f"FFN intermediate dimension: {llama_config.intermediate_size}")
    print(f"Maximum position embeddings: {llama_config.max_position_embeddings}")