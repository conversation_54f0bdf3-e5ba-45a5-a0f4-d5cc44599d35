import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, random_split
from torchvision import datasets, transforms
import os
from torch.utils.tensorboard import SummaryWriter
import sys
from tqdm import tqdm

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
proto_simclr_path = os.path.join(project_root, 'ProtoSimCLR')
sys.path.insert(0, project_root)
sys.path.insert(0, proto_simclr_path)

try:
    from ProtoSimCLR.models.tangtangmodel import MergedModel, seq_branch_encoder, img_branch_encoder
except ImportError:
    try:
        from models.tangtangmodel import MergedModel, img_branch_encoder
    except ImportError:
        raise ImportError("无法导入模型组件，请检查文件路径和Python路径设置")


def get_data_loaders(data_dir, batch_size=32, num_workers=4, val_split=0.2):
    """
    创建训练和验证数据加载器
    :param data_dir: 数据集根目录，包含train和test子目录
    :param batch_size: 批次大小
    :param num_workers: 数据加载线程数
    :param val_split: 验证集比例（从训练集中划分）
    :return: (train_loader, val_loader, test_loader, num_classes)
    """
    # 数据预处理和增强
    train_transform = transforms.Compose([
        transforms.Resize((32, 32)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    val_transform = transforms.Compose([
        transforms.Resize((32, 32)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    # 加载训练数据集
    train_dataset_full = datasets.ImageFolder(
        root=os.path.join(data_dir, 'train'),
        transform=train_transform
    )

    # 从训练集中划分验证集
    val_size = int(len(train_dataset_full) * val_split)
    train_size = len(train_dataset_full) - val_size
    train_dataset, val_dataset = random_split(
        train_dataset_full,
        [train_size, val_size],
        generator=torch.Generator().manual_seed(42)  # 固定种子以确保可重复性
    )

    # 为验证集设置不同的transform
    # 创建一个包含正确transform的验证数据集
    val_dataset.dataset = train_dataset_full
    val_dataset.transform = val_transform

    # 加载测试数据集
    test_dataset = datasets.ImageFolder(
        root=os.path.join(data_dir, 'test'),
        transform=val_transform
    )

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )

    return train_loader, val_loader, test_loader, len(train_dataset_full.classes)


class ClassificationHead(nn.Module):
    """分类头，添加到融合模型之后"""

    def __init__(self, fusion_dim=256, num_classes=10):
        super(ClassificationHead, self).__init__()
        self.classifier = nn.Sequential(
            nn.Linear(fusion_dim, 512),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(512, num_classes)
        )

    def forward(self, x):
        return self.classifier(x)


class ImageClassifier(nn.Module):
    """完整的图像分类模型"""

    def __init__(self, merged_model, classification_head):
        super(ImageClassifier, self).__init__()
        self.feature_extractor = merged_model
        self.classifier = classification_head

    def forward(self, x):
        features = self.feature_extractor(x)
        logits = self.classifier(features)
        return logits


def evaluate_model(model, data_loader, device='cuda', desc="Evaluating"):
    """
    在给定数据集上评估模型
    :param model: 待评估的模型
    :param data_loader: 数据加载器
    :param device: 设备
    :param desc: 进度条描述
    :return: (准确率, 平均损失)
    """
    model.eval()
    criterion = nn.CrossEntropyLoss()
    correct = 0
    total = 0
    total_loss = 0.0

    # 使用tqdm显示进度
    with torch.no_grad():
        pbar = tqdm(data_loader, desc=desc, leave=False)
        for data, targets in pbar:
            data, targets = data.to(device), targets.to(device)
            outputs = model(data)
            loss = criterion(outputs, targets)

            total_loss += loss.item()
            _, predicted = outputs.max(1)
            total += targets.size(0)
            correct += predicted.eq(targets).sum().item()

            # 动态更新准确率
            current_acc = 100. * correct / total
            pbar.set_postfix({'Accuracy': f'{current_acc:.2f}%'})

    accuracy = 100. * correct / total
    avg_loss = total_loss / len(data_loader)

    return accuracy, avg_loss


def train_model(model, train_loader, val_loader, num_epochs=50, learning_rate=0.001, device='cuda'):
    """
    训练模型
    :param model: 待训练的模型
    :param train_loader: 训练数据加载器
    :param val_loader: 验证数据加载器
    :param num_epochs: 训练轮数
    :param learning_rate: 学习率
    :param device: 训练设备
    """
    # 损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=20, gamma=0.1)

    # TensorBoard记录
    writer = SummaryWriter('runs/image_classification')

    # 将模型移到指定设备
    model.to(device)

    best_val_acc = 0.0

    # 外层进度条显示epoch
    epoch_pbar = tqdm(range(num_epochs), desc="Epochs", leave=True)

    for epoch in epoch_pbar:
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0

        # 内层进度条显示训练批次
        train_pbar = tqdm(train_loader, desc=f"Epoch {epoch + 1}/{num_epochs} [Train]", leave=False)
        for batch_idx, (data, targets) in enumerate(train_pbar):
            data, targets = data.to(device), targets.to(device)

            # 前向传播
            optimizer.zero_grad()
            outputs = model(data)
            loss = criterion(outputs, targets)

            # 反向传播
            loss.backward()
            optimizer.step()

            # 统计信息
            train_loss += loss.item()
            _, predicted = outputs.max(1)
            train_total += targets.size(0)
            train_correct += predicted.eq(targets).sum().item()

            # 动态更新训练进度和准确率
            current_train_acc = 100. * train_correct / train_total
            current_train_loss = train_loss / (batch_idx + 1)
            train_pbar.set_postfix({
                'Loss': f'{current_train_loss:.4f}',
                'Acc': f'{current_train_acc:.2f}%'
            })

        # 计算训练准确率
        train_acc = 100. * train_correct / train_total
        avg_train_loss = train_loss / len(train_loader)

        # 验证阶段
        val_acc, avg_val_loss = evaluate_model(model, val_loader, device, f"Epoch {epoch + 1}/{num_epochs} [Val]")

        # 更新学习率
        scheduler.step()

        # 记录到TensorBoard
        writer.add_scalar('Loss/Train', avg_train_loss, epoch)
        writer.add_scalar('Loss/Val', avg_val_loss, epoch)
        writer.add_scalar('Accuracy/Train', train_acc, epoch)
        writer.add_scalar('Accuracy/Val', val_acc, epoch)

        # 更新epoch进度条
        epoch_pbar.set_postfix({
            'Train Loss': f'{avg_train_loss:.4f}',
            'Train Acc': f'{train_acc:.2f}%',
            'Val Loss': f'{avg_val_loss:.4f}',
            'Val Acc': f'{val_acc:.2f}%'
        })

        print(f'Epoch [{epoch + 1}/{num_epochs}] '
              f'Train Loss: {avg_train_loss:.4f}, Train Acc: {train_acc:.2f}%, '
              f'Val Loss: {avg_val_loss:.4f}, Val Acc: {val_acc:.2f}%')

        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
            }, 'best_model.pth')
            print(f'Saved best model with validation accuracy: {val_acc:.2f}%')

    writer.close()
    print(f'Finished Training. Best validation accuracy: {best_val_acc:.2f}%')

    return best_val_acc


def test_model(model_path, model, test_loader, device='cuda'):
    """
    在测试集上评估模型
    :param model_path: 模型路径
    :param model: 模型实例
    :param test_loader: 测试数据加载器
    :param device: 设备
    """
    # 加载最佳模型
    checkpoint = torch.load(model_path)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device)

    # 在测试集上评估
    test_acc, test_loss = evaluate_model(model, test_loader, device, "Testing")
    print(f'Test Accuracy: {test_acc:.2f}%, Test Loss: {test_loss:.4f}')


def main():
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')

    # 数据集路径（请根据实际情况修改）
    data_dir = './data/cifar10/'  # 修改为你的数据集路径

    # 获取数据加载器
    train_loader, val_loader, test_loader, num_classes = get_data_loaders(data_dir, batch_size=64)
    print(f'Number of classes: {num_classes}')
    print(f'Training samples: {len(train_loader.dataset)}')
    print(f'Validation samples: {len(val_loader.dataset)}')
    print(f'Test samples: {len(test_loader.dataset)}')
    from ProtoSimCLR.models.bert_encoder import BertEncoder,BertEncoderConfig
    seq_branch_encoder = BertEncoder(BertEncoderConfig())
    # 创建模型
    merged_model = MergedModel(
        seq_branch_encoder=seq_branch_encoder,
        img_branch_encoder=img_branch_encoder,
        seq_dim=128,
        img_dim=128,
        fusion_dim=256
    )

    classification_head = ClassificationHead(fusion_dim=256, num_classes=num_classes)
    model = ImageClassifier(merged_model, classification_head)

    # 打印模型结构
    print(model)

    # 开始训练
    best_val_acc = train_model(model, train_loader, val_loader, num_epochs=50, learning_rate=0.001, device=device)

    # 在测试集上评估
    if os.path.exists('best_model.pth'):
        test_model('best_model.pth', model, test_loader, device)


if __name__ == '__main__':
    main()
