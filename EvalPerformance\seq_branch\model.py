import torch
import torch.nn as nn
from bert_encoder import <PERSON><PERSON><PERSON><PERSON>,BertEncoderConfig
class SeqBranchCLassifier(nn.Module):
    def __init__(self, **kwargs):
        super().__init__()
        self.num_classes = kwargs["num_classes"]
        self.pretrained = kwargs["pretrained_path"]
        if self.pretrained is None:
            print("No pretrained weights provided")
        if self.num_classes is None:
            raise ValueError("num_classes must be provided")
        config = BertEncoderConfig()

        self.backbone = BertEncoder(config)
        self.encoder_in_features = self.backbone.final_projection.in_features
        self.backbone.final_projection = nn.Identity()
        self.classifier = nn.Linear(self.encoder_in_features, self.num_classes)
        if self.pretrained:
            self.load_pretrained_weights(self.pretrained)
    def freeze_backbone(self):
        for param in self.backbone.parameters():
            param.requires_grad = False
        for param in self.classifier.parameters():
            param.requires_grad = True
        print("Backbone frozen")

    def unfreeze_backbone(self):
        for param in self.backbone.parameters():
            param.requires_grad = True
        for param in self.classifier.parameters():
            param.requires_grad = True
        print("Backbone unfrozen")

    def load_pretrained_weights(self, pretrained_path):
        checkpoint = torch.load(pretrained_path, map_location='cpu')
        # 处理不同格式的checkpoint
        if 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
        elif 'state_dict' in checkpoint:
            state_dict = checkpoint['state_dict']
        elif 'model' in checkpoint:
            state_dict = checkpoint['model']
        else:
            state_dict = checkpoint

        # 移除可能存在的module.前缀 (如果是DataParallel保存的)
        state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
        # 不加载分类头的权重
        filtered_state_dict = {k: v for k, v in state_dict.items() if not k.startswith('classifier')}
        # 严格加载匹配的参数
        msg = self.backbone.load_state_dict(filtered_state_dict, strict=False)
        print(f'Missing keys: {msg.missing_keys}')
        print(f'Unexpected keys: {msg.unexpected_keys}')
        return self.backbone

    def forward(self, input_bytes, attention_mask=None):
        x = self.backbone(input_bytes, attention_mask)
        x = self.classifier(x)
        return x