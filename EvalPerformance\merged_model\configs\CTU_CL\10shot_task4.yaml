CUDA_VISIBLE_DEVICES: 3

# 数据路径配置
img_train_data_path: "../../data/CTU_10_shot/dataset4"
img_val_data_path: "../../data/CTU_Processed/CTU_IMG/Test"
seq_train_data_path: "../../data/CTU_10_shot/SeqBranch/dataset4"
seq_val_data_path: "../../data/CTU_Processed/CTU_SEQUENCE/Test"

# 预训练模型路径
img_pretrained_path: "./ckpt/resnet18.pth.tar"
seq_pretrained_path: "./ckpt/bert_200epoch.pth.tar"

# merged_model
fusion_dim: 256
seq_length: 1024

# 训练配置
img_size: 32
batch_size: 64
learning_rate: 0.001
freeze_train: True
finetune_epochs: 150
freeze_epoch: 20

# 保存配置
save_dir: "./logs"
log_name: "CTU_MERGED_CL_10/task4"
