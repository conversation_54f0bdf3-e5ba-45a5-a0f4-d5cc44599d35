"""
ProtoSimCLR元学习训练主脚本
"""

import argparse
import os
import torch
from torch.utils.data import DataLoader

from config_parser import ConfigParser, create_transforms, get_dataset_class
from utils.dataset import ProtoSCLDataset
from utils.util import set_seed, create_tensorboard_logger, AverageMeter, Timer
import tqdm
from torch.cuda.amp import GradScaler, autocast
def train_one_epoch(**kwargs):
    """
    训练一个epoch
    """
    model = kwargs['model']
    train_loader = kwargs['train_loader']
    optimizer = kwargs['optimizer']
    criterion = kwargs['criterion']
    device = kwargs['device']
    logger = kwargs.get('logger', None)
    epoch = kwargs.get('epoch', 0)
    log_freq = kwargs.get('log_freq', 100)

    model.train()

    # 使用AverageMeter来跟踪指标
    loss_meter = AverageMeter()
    accuracy_meter = AverageMeter()

    # 创建进度条
    progress_bar = tqdm.tqdm(train_loader, desc=f"Training Epoch {epoch+1}", leave=False)

    for episode_idx,item in enumerate(progress_bar):
        # episode_data是一个包含单个episode的batch
        episode, cl_data = item
        support_x, support_y, query_x, query_y = episode
        # 前向传播
        logits = model.episode_forward(support_x, query_x)
        loss = criterion(logits, query_y)

        # 如果使用对比损失，计算对比损失
        features = model(cl_data)
        seq_embedding = features['seq_embedding']
        img_embedding = features['img_embedding']
        seq_logits, labels = model.info_nce_loss(seq_embedding)
        seq_logits = seq_logits.to(device)  # 确保对比损失的logits在正确的设备上
        labels = labels.to(device)  # 确保对比损失的标签在正确的设备上
        seq_loss = criterion(seq_logits, labels)
        img_logits, labels = model.info_nce_loss(img_embedding)
        img_logits = img_logits.to(device)
        img_loss = criterion(img_logits, labels)
        loss += 0.1*seq_loss + 0.1*img_loss
        # 计算准确率
        with torch.no_grad():
            pred = logits.argmax(dim=1)
            accuracy = (pred == query_y).float().mean().item()

        # 反向传播和优化
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        # 更新指标
        loss_meter.update(loss.item())
        accuracy_meter.update(accuracy)

        # 更新进度条
        progress_bar.set_postfix({
            'Loss': f'{loss_meter.avg:.4f}',
            'Acc': f'{accuracy_meter.avg:.4f}'
        })

        # 记录到tensorboard
        if logger is not None and episode_idx % log_freq == 0:
            global_step = epoch * len(train_loader) + episode_idx
            logger.log_scalar('Train/Episode_Loss', loss.item(), global_step)
            logger.log_scalar('Train/Episode_Accuracy', accuracy, global_step)
            logger.log_scalar('Train/Avg_Loss', loss_meter.avg, global_step)
            logger.log_scalar('Train/Avg_Accuracy', accuracy_meter.avg, global_step)

            # 记录学习率
            logger.log_learning_rate(optimizer, global_step)

            # 记录GPU内存使用情况
            logger.log_gpu_memory(global_step)

    return loss_meter.avg, accuracy_meter.avg

def validate_one_epoch(**kwargs):
    """
    验证一个epoch
    """
    model = kwargs['model']
    val_loader = kwargs['val_loader']
    criterion = kwargs['criterion']
    device = kwargs['device']
    logger = kwargs.get('logger', None)
    epoch = kwargs.get('epoch', 0)

    model.eval()

    # 使用AverageMeter来跟踪指标
    loss_meter = AverageMeter()
    accuracy_meter = AverageMeter()

    # 用于计算更详细的指标
    all_predictions = []
    all_targets = []

    with torch.no_grad():
        progress_bar = tqdm.tqdm(val_loader, desc=f"Validation Epoch {epoch+1}", leave=False)

        for episode_idx,episode in enumerate(progress_bar):
            # episode_data是一个包含单个episode的batch
            support_x, support_y, query_x, query_y = episode

            # 去除batch维度 (因为batch_size=1)
            support_x = support_x.squeeze(0)
            support_y = support_y.squeeze(0)
            query_x = query_x.squeeze(0)
            query_y = query_y.squeeze(0)

            # 移动到设备
            support_x = support_x.to(device)
            query_x = query_x.to(device)
            query_y = query_y.to(device)

            # 前向传播
            logits = model.episode_forward(support_x, query_x)
            loss = criterion(logits, query_y)

            # 计算准确率
            pred = logits.argmax(dim=1)
            accuracy = (pred == query_y).float().mean().item()

            # 更新指标
            loss_meter.update(loss.item())
            accuracy_meter.update(accuracy)

            # 收集预测和真实标签用于计算详细指标
            all_predictions.extend(pred.cpu().numpy())
            all_targets.extend(query_y.cpu().numpy())

            # 更新进度条
            progress_bar.set_postfix({
                'Loss': f'{loss_meter.avg:.4f}',
                'Acc': f'{accuracy_meter.avg:.4f}'
            })

    # 计算额外的评估指标
    metrics = {}
    if len(all_predictions) > 0:
        try:
            from sklearn.metrics import precision_score, recall_score, f1_score
            metrics['precision'] = precision_score(all_targets, all_predictions, average='weighted', zero_division=0)
            metrics['recall'] = recall_score(all_targets, all_predictions, average='weighted', zero_division=0)
            metrics['f1_score'] = f1_score(all_targets, all_predictions, average='weighted', zero_division=0)
        except ImportError:
            print("Warning: sklearn not available, skipping detailed metrics")
            metrics = {'precision': 0.0, 'recall': 0.0, 'f1_score': 0.0}
        except Exception as e:
            print(f"Warning: Could not compute detailed metrics: {e}")
            metrics = {'precision': 0.0, 'recall': 0.0, 'f1_score': 0.0}

    # 记录验证指标到tensorboard
    if logger is not None:
        global_step = epoch
        logger.log_scalar('Validation/Loss', loss_meter.avg, global_step)
        logger.log_scalar('Validation/Accuracy', accuracy_meter.avg, global_step)

        # 记录详细指标
        for metric_name, metric_value in metrics.items():
            logger.log_scalar(f'Validation/{metric_name.capitalize()}', metric_value, global_step)

    return loss_meter.avg, accuracy_meter.avg, metrics

def main():
    """主训练函数"""
    parser = argparse.ArgumentParser(description='ProtoSimCLR Meta Training')
    parser.add_argument('--config', type=str, default='configs/multiclass.yaml',
                        help='Path to config file')

    args = parser.parse_args()
    # 加载配置
    config_parser = ConfigParser(args.config)
    config_parser.print_config()
    cuda_visible_devices = config_parser.get_cuda_visible_devices()
    if cuda_visible_devices:
        os.environ['CUDA_VISIBLE_DEVICES'] = cuda_visible_devices
        print(f"Using CUDA_VISIBLE_DEVICES: {cuda_visible_devices}")

    # 创建数据加载器
    print("Creating data loaders...")

    # 获取配置参数
    data_config = config_parser.get_data_config()
    ProtoSCL_config = config_parser.get_ProtoSCL_config()
    training_config = config_parser.get_training_config()

    train_dataset = ProtoSCLDataset(
        img_root=data_config['img_root'],
        seq_root=data_config['seq_root'],
        **ProtoSCL_config,  # 传递n_way, k_shot等参数
    )
    val_dataset = ProtoSCLDataset(
        img_root=data_config['val_img_root'],
        seq_root=data_config['val_seq_root'],
        **ProtoSCL_config,
    )
    # 简化DataLoader，batch_size=1因为每个样本就是一个完整的episode
    def collate_fn(batch):
        episode,cl_data = batch[0]
        return episode,cl_data
    train_loader = DataLoader(
        train_dataset,
        batch_size=1,  # 每个episode作为一个batch
        shuffle=True,
        num_workers=training_config.get('num_workers', 4),
        collate_fn=collate_fn
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=1,  # 每个episode作为一个batch
        shuffle=False,
        num_workers=training_config.get('num_workers', 4),
        collate_fn=collate_fn
    )
    from models.bert_encoder import BertEncoder, BertEncoderConfig
    from torchvision.models import resnet18
    from models.merged_model import MergedModel
    from models.protonet import ProtoSCL
    import torch.nn as nn
    seq_branch_encoder = BertEncoder(BertEncoderConfig())
    img_branch_encoder = resnet18(pretrained=False,num_classes=128)
    seq_dim = seq_branch_encoder.final_projection.in_features
    img_dim = img_branch_encoder.fc.in_features
    seq_branch_encoder.final_projection = nn.Identity()
    img_branch_encoder.fc = nn.Identity()
    backbone_cfg = config_parser.get_backbone_config()
    backbone = MergedModel(seq_branch_encoder=seq_branch_encoder,
                           img_branch_encoder=img_branch_encoder,
                           seq_dim=seq_dim,
                           img_dim=img_dim,
                           seq_pretrained_path=backbone_cfg.get('pretrained_path',None),
                           img_pretrained_path=backbone_cfg.get('pretrained_path',None))
    model = ProtoSCL(backbone=backbone, **ProtoSCL_config)
    device = torch.device(training_config['device'])
    model.to(device)

    # 只优化未冻结的参数
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    criterion = torch.nn.CrossEntropyLoss()


    # 设置随机种子
    set_seed(training_config.get('seed', 42))

    # 创建tensorboard日志记录器
    logger = create_tensorboard_logger(config_parser)
    # 获取日志配置
    logging_config = config_parser.get_logging_config()
    log_freq = logging_config.get('log_freq', 100)
    # 训练计时器
    timer = Timer()
    timer.start()
    for epoch in range(training_config.get('epochs', 120)):
        print(f"Epoch {epoch + 1}/{training_config.get('epochs', 120)}")

        # 训练
        train_loss, train_accuracy = train_one_epoch(
            model=model,
            train_loader=train_loader,
            optimizer=optimizer,
            criterion=criterion,
            device=device,
            logger=logger,
            epoch=epoch,
            log_freq=log_freq,
        )
        print(f"Train Loss: {train_loss:.4f}, Train Accuracy: {train_accuracy:.4f}")

        # 验证
        val_loss, val_accuracy, val_metrics = validate_one_epoch(
            model=model,
            val_loader=val_loader,
            criterion=criterion,
            device=device,
            logger=logger,
            epoch=epoch,
        )
        print(f"Val Loss: {val_loss:.4f}, Val Accuracy: {val_accuracy:.4f}")

        # 打印详细指标
        if val_metrics:
            metrics_str = ", ".join([f"{k.capitalize()}: {v:.4f}" for k, v in val_metrics.items()])
            print(f"Val Metrics: {metrics_str}")

        # 记录epoch级别的指标到tensorboard
        logger.log_scalar('Epoch/Train_Loss', train_loss, epoch)
        logger.log_scalar('Epoch/Train_Accuracy', train_accuracy, epoch)
        logger.log_scalar('Epoch/Val_Loss', val_loss, epoch)
        logger.log_scalar('Epoch/Val_Accuracy', val_accuracy, epoch)

        # 记录训练时间
        elapsed_time = timer.elapsed()
        logger.log_scalar('Time/Elapsed_Hours', elapsed_time / 3600, epoch)

        # 记录模型参数统计（每10个epoch记录一次）
        if epoch % 10 == 0:
            logger.log_model_parameters(model, epoch)

        # 保存模型
        save_freq = config_parser.get('logging', {}).get('save_freq', 15)
        if (epoch + 1) % save_freq == 0:
            save_dir = config_parser.get('logging', {}).get('save_dir', './checkpoints')
            os.makedirs(save_dir, exist_ok=True)
            checkpoint_path = os.path.join(save_dir, f'checkpoint_epoch_{epoch + 1}.pth')
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': train_loss,
                'val_loss': val_loss,
                'train_accuracy': train_accuracy,
                'val_accuracy': val_accuracy,
                'config': config_parser.config
            }, checkpoint_path)
            print(f"Model saved to {checkpoint_path}")

    # 训练完成，关闭logger
    total_time = timer.stop()
    print(f"\n🎉 Training completed! Total time: {total_time/3600:.2f} hours")
    logger.log_text('Training/Status', f'Training completed in {total_time/3600:.2f} hours', 0)
    logger.close()




if __name__ == '__main__':
    main()

