from torch.utils.data import Dataset
import pickle
import numpy as np
import os
import torch
class SequenceDataset(Dataset):
    def __init__(self, root_folder, transform=None, sequence_length=1024):
        self.root_folder = root_folder
        self.transform = transform
        self.sequence_length = sequence_length
        self.seq_path = []
        self.labels = []
        self.class_idx = {name : i for i, name in enumerate(sorted(os.listdir(root_folder)))}
        # 遍历文件夹中的所有PKL文件
        for class_name in os.listdir(root_folder):
            class_dir = os.path.join(root_folder, class_name)
            if os.path.isdir(class_dir):
                # 递归遍历class_dir下所有文件
                for root, _, files in os.walk(class_dir):
                    for filename in files:
                        if filename.endswith('.pkl'):
                            self.seq_path.append(os.path.join(root, filename))
                            self.labels.append(self.class_idx[class_name])

    def __len__(self):
        return len(self.seq_path)

    def __getitem__(self, idx):
        file_path = self.seq_path[idx]

        # 读取PKL文件
        with open(file_path, 'rb') as f:
            data = pickle.load(f)

        # 处理字节流数据
        if isinstance(data, bytes):
            # 将字节转换为数值数组
            byte_array = np.frombuffer(data, dtype=np.uint8)
        elif isinstance(data, np.ndarray) and data.dtype == np.uint8:
            # 如果已经是字节数组
            byte_array = data
        else:
            # 尝试转换为字节数组
            byte_array = np.array(data, dtype=np.uint8)
        # 确保序列长度一致
        if len(byte_array) != self.sequence_length:
            raise ValueError(f"Sequence length mismatch: Expected {self.sequence_length}, got {len(byte_array)}")
        # 转换为tensor并归一化到[0,1]范围
        sequence = torch.from_numpy(byte_array).float() / 255.0
        # 应用变换
        if self.transform:
            sequence = self.transform(sequence)
        label = self.labels[idx]
        return sequence, label