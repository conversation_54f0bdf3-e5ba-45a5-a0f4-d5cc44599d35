# -*- coding: utf-8 -*-
"""
PCAP Processing Pipeline
Orchestrates the four-stage processing workflow:
1. Session splitting (AL layer only)
2. Traffic anonymization (AL layer only)
3. Feature extraction (AL/L7/L5 layers from anonymized data)
4. RGB image generation
"""

import logging
import time
import sys
from pathlib import Path

# Handle both relative and absolute imports
try:
    from .session_splitter import SessionSplitter
    from .traffic_anonymizer import TrafficAnonymizer
    from .rgb_generator import RGBGenerator
except ImportError:
    # Add current directory to path for direct execution
    current_dir = Path(__file__).parent
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))

    from splitpcap import SessionSplitter
    from anonymizer import TrafficAnonymizer
    from feature_extractor import FeatureExtractor
    from pcap2rgb import RGBGenerator


class PCAPProcessingPipeline:
    """
    Complete PCAP processing pipeline combining all four stages
    """

    def __init__(self,
                 input_dir="./pcap_dataset",
                 # split_dir="./temp/splitpcap",
                 # anonymous_dir="./temp/anonymous",
                 # features_dir="./temp/features",
                 final_dir="./img_dataset",
                 max_sessions=1000,
                 image_size=32):
        """
        Initialize PCAP processing pipeline

        Args:
            input_dir: Input directory containing PCAP files
            split_dir: Directory for session-split files
            anonymous_dir: Directory for anonymized files
            features_dir: Directory for extracted features
            final_dir: Directory for final RGB images
            max_sessions: Maximum sessions per PCAP file
            image_size: Size of output RGB images
        """
        self.input_dir = Path(input_dir)
        split_dir = f"./temp/{input_dir}/splitpcap"
        anonymous_dir = f"./temp/{input_dir}/anonymous"
        features_dir = f"./temp/{input_dir}/features"
        self.split_dir = Path(split_dir)
        self.anonymous_dir = Path(anonymous_dir)
        self.features_dir = Path(features_dir)
        self.final_dir = Path(final_dir)

        # Initialize stage processors
        self.session_splitter = SessionSplitter(
            output_base_dir=str(split_dir),
            max_sessions=max_sessions
        )

        self.traffic_anonymizer = TrafficAnonymizer(
            input_base_dir=str(split_dir),
            output_base_dir=str(anonymous_dir)
        )

        self.feature_extractor = FeatureExtractor(
            input_base_dir=str(anonymous_dir),
            output_base_dir=str(features_dir)
        )

        self.rgb_generator = RGBGenerator(
            input_base_dir=str(features_dir),
            output_base_dir=str(final_dir),
            image_size=image_size
        )
        
        self.logger = self._setup_logger()
    
    def _setup_logger(self):
        """Setup logging"""
        logger = logging.getLogger('PCAPPipeline')
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
    
    def run_stage1_session_splitting(self):
        """
        Run Stage 1: Session-based PCAP splitting
        """
        self.logger.info("=" * 60)
        self.logger.info("STAGE 1: SESSION SPLITTING")
        self.logger.info("=" * 60)
        
        start_time = time.time()
        
        try:
            self.session_splitter.process_directory(self.input_dir)
            
            elapsed_time = time.time() - start_time
            self.logger.info(f"Stage 1 completed in {elapsed_time:.2f} seconds")
            return True
            
        except Exception as e:
            self.logger.error(f"Stage 1 failed: {e}")
            return False
    
    def run_stage2_anonymization(self):
        """
        Run Stage 2: Traffic anonymization
        """
        self.logger.info("=" * 60)
        self.logger.info("STAGE 2: TRAFFIC ANONYMIZATION")
        self.logger.info("=" * 60)
        
        start_time = time.time()
        
        try:
            self.traffic_anonymizer.process_directory()
            
            elapsed_time = time.time() - start_time
            self.logger.info(f"Stage 2 completed in {elapsed_time:.2f} seconds")
            return True
            
        except Exception as e:
            self.logger.error(f"Stage 2 failed: {e}")
            return False
    
    def run_stage3_feature_extraction(self):
        """
        Run Stage 3: Feature extraction (AL/L7/L5 layers)
        """
        self.logger.info("=" * 60)
        self.logger.info("STAGE 3: FEATURE EXTRACTION")
        self.logger.info("=" * 60)

        start_time = time.time()

        try:
            self.feature_extractor.process_directory()

            elapsed_time = time.time() - start_time
            self.logger.info(f"Stage 3 completed in {elapsed_time:.2f} seconds")
            return True

        except Exception as e:
            self.logger.error(f"Stage 3 failed: {e}")
            return False

    def run_stage4_rgb_generation(self):
        """
        Run Stage 4: RGB image generation
        """
        self.logger.info("=" * 60)
        self.logger.info("STAGE 4: RGB IMAGE GENERATION")
        self.logger.info("=" * 60)

        start_time = time.time()

        try:
            self.rgb_generator.process_directory()

            elapsed_time = time.time() - start_time
            self.logger.info(f"Stage 4 completed in {elapsed_time:.2f} seconds")
            return True

        except Exception as e:
            self.logger.error(f"Stage 4 failed: {e}")
            return False
    
    def run_full_pipeline(self, skip_stages=None):
        """
        Run the complete four-stage pipeline

        Args:
            skip_stages: List of stages to skip (e.g., [1, 2] to skip stages 1 and 2)
        """
        if skip_stages is None:
            skip_stages = []

        self.logger.info("*" * 80)
        self.logger.info("STARTING PCAP PROCESSING PIPELINE")
        self.logger.info("*" * 80)
        self.logger.info(f"Input directory: {self.input_dir}")
        self.logger.info(f"Split directory: {self.split_dir}")
        self.logger.info(f"Anonymous directory: {self.anonymous_dir}")
        self.logger.info(f"Features directory: {self.features_dir}")
        self.logger.info(f"Final directory: {self.final_dir}")

        pipeline_start_time = time.time()
        stages_completed = 0

        # Stage 1: Session Splitting
        if 1 not in skip_stages:
            if self.run_stage1_session_splitting():
                stages_completed += 1
            else:
                self.logger.error("Pipeline stopped due to Stage 1 failure")
                return False
        else:
            self.logger.info("Skipping Stage 1: Session Splitting")

        # Stage 2: Traffic Anonymization
        if 2 not in skip_stages:
            if self.run_stage2_anonymization():
                stages_completed += 1
            else:
                self.logger.error("Pipeline stopped due to Stage 2 failure")
                return False
        else:
            self.logger.info("Skipping Stage 2: Traffic Anonymization")

        # Stage 3: Feature Extraction
        if 3 not in skip_stages:
            if self.run_stage3_feature_extraction():
                stages_completed += 1
            else:
                self.logger.error("Pipeline stopped due to Stage 3 failure")
                return False
        else:
            self.logger.info("Skipping Stage 3: Feature Extraction")

        # Stage 4: RGB Generation
        if 4 not in skip_stages:
            if self.run_stage4_rgb_generation():
                stages_completed += 1
            else:
                self.logger.error("Pipeline stopped due to Stage 4 failure")
                return False
        else:
            self.logger.info("Skipping Stage 4: RGB Generation")
        
        # Pipeline completion
        total_elapsed = time.time() - pipeline_start_time
        
        self.logger.info("*" * 80)
        self.logger.info("PIPELINE COMPLETED SUCCESSFULLY")
        self.logger.info("*" * 80)
        self.logger.info(f"Stages completed: {stages_completed}")
        self.logger.info(f"Total processing time: {total_elapsed:.2f} seconds")
        
        # Summary statistics
        self._print_summary()
        
        return True
    
    def _print_summary(self):
        """Print pipeline processing summary"""
        self.logger.info("\nPROCESSING SUMMARY:")
        self.logger.info("-" * 40)
        
        # Count input PCAP files
        if self.input_dir.exists():
            pcap_files = list(self.input_dir.rglob("*.pcap")) + list(self.input_dir.rglob("*.pcapng"))
            self.logger.info(f"Input PCAP files: {len(pcap_files)}")
        
        # Count split sessions
        if self.split_dir.exists():
            session_dirs = []
            for path in self.split_dir.rglob("*"):
                if path.is_dir() and (path / 'al').exists():
                    session_dirs.append(path)
            self.logger.info(f"Session directories created: {len(session_dirs)}")

        # Count anonymized files
        if self.anonymous_dir.exists():
            anon_files = list(self.anonymous_dir.rglob("*.pcap"))
            self.logger.info(f"Anonymized PCAP files: {len(anon_files)}")

        # Count feature files
        if self.features_dir.exists():
            feature_dirs = []
            for path in self.features_dir.rglob("*"):
                if path.is_dir() and all((path / layer).exists() for layer in ['al', 'l7', 'l5']):
                    feature_dirs.append(path)
            self.logger.info(f"Feature directories created: {len(feature_dirs)}")

        # Count final RGB images
        if self.final_dir.exists():
            rgb_images = list(self.final_dir.rglob("*.png"))
            self.logger.info(f"RGB images generated: {len(rgb_images)}")
    
    def cleanup_intermediate_files(self, keep_final=True):
        """
        Clean up intermediate processing files
        
        Args:
            keep_final: Whether to keep final RGB images
        """
        import shutil
        
        self.logger.info("Cleaning up intermediate files...")
        
        # Remove split directory
        if self.split_dir.exists():
            shutil.rmtree(self.split_dir)
            self.logger.info(f"Removed: {self.split_dir}")

        # Remove anonymous directory
        if self.anonymous_dir.exists():
            shutil.rmtree(self.anonymous_dir)
            self.logger.info(f"Removed: {self.anonymous_dir}")

        # Remove features directory
        if self.features_dir.exists():
            shutil.rmtree(self.features_dir)
            self.logger.info(f"Removed: {self.features_dir}")

        # Optionally remove final directory
        if not keep_final and self.final_dir.exists():
            shutil.rmtree(self.final_dir)
            self.logger.info(f"Removed: {self.final_dir}")
        
        self.logger.info("Cleanup completed")


def main():
    """
    Main function for command-line usage
    """
    import argparse
    
    parser = argparse.ArgumentParser(description='PCAP Processing Pipeline')
    parser.add_argument('--input', '-i', default='./pcap_dataset',
                       help='Input directory containing PCAP files')
    parser.add_argument('--output', '-o', default='./img_dataset/ISAC218',
                       help='Output directory for RGB images')
    parser.add_argument('--max-sessions', type=int, default=6000,
                       help='Maximum sessions per PCAP file')
    parser.add_argument('--image-size', type=int, default=32,
                       help='Size of output RGB images')
    parser.add_argument('--skip-stages', nargs='*', type=int, choices=[1, 2, 3, 4],
                       help='Stages to skip (1=splitting, 2=anonymization, 3=features, 4=rgb)')
    parser.add_argument('--cleanup', action='store_true',
                       help='Clean up intermediate files after processing')
    
    args = parser.parse_args()
    
    # Initialize pipeline
    pipeline = PCAPProcessingPipeline(
        input_dir=args.input,
        final_dir=args.output,
        max_sessions=args.max_sessions,
        image_size=args.image_size
    )
    
    # Run pipeline
    success = pipeline.run_full_pipeline(skip_stages=args.skip_stages)
    
    # Cleanup if requested
    if args.cleanup and success:
        pipeline.cleanup_intermediate_files()
    
    return 0 if success else 1


if __name__ == "__main__":

    exit(main())
