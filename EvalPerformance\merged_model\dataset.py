import torch
from torch.utils.data import Dataset
from torchvision import transforms
from PIL import Image
import os
import numpy as np
import pickle


class DualBranchDataset(Dataset):
    """双分支数据集：同时加载图像和序列数据"""
    
    def __init__(self, **kargs):
        """
        :param img_root: 图像数据根目录
        :param seq_root: 序列数据根目录  
        :param transform: 图像变换
        :param seq_length: 序列长度
        """
        self.img_root = kargs.get('img_root', None)
        self.seq_root = kargs.get('seq_root', None)
        self.img_branch_transform = kargs.get('img_branch_transform', None)
        self.seq_length = kargs.get('seq_length', 1024)
        
        # 获取类别列表
        self.classes = sorted([d for d in os.listdir(self.img_root)])
        self.class_to_idx = {cls_name: idx for idx, cls_name in enumerate(self.classes)}
        
        # 构建样本列表
        self.samples = []
        self._build_samples()
        
    def _build_samples(self):
        """构建样本列表，确保图像和序列数据一一对应"""
        # 遍历文件夹中的所有PKL文件
        # 基于seq路径来得到图片路径
        for class_name in os.listdir(self.seq_root):
            class_dir = os.path.join(self.seq_root, class_name)
            if os.path.isdir(class_dir):
                # 递归遍历class_dir下所有文件
                for root, _, files in os.walk(class_dir):
                    for filename in files:
                        if filename.endswith('.pkl'):
                            seq_path = os.path.join(root, filename)
                            img_path = os.path.join(self.img_root, class_name, filename[:-4] + '.png')
                            if not os.path.exists(img_path):
                                raise FileNotFoundError(f"Image file not found: {img_path}")
                            label = self.class_to_idx[class_name]
                            self.samples.append({
                                'img_path': img_path,
                                'seq_path': seq_path,
                                'class_idx': label,
                            })
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        
        # 加载图像
        img = Image.open(sample['img_path']).convert('RGB')
        if self.img_branch_transform:
            img = self.img_branch_transform(img)
        
        # 加载序列数据
        try:
            with open(sample['seq_path'], 'rb') as f:
                seq_data = pickle.load(f)

                # 处理字节流数据
                if isinstance(seq_data, bytes):
                    # 将字节转换为数值数组
                    byte_array = np.frombuffer(seq_data, dtype=np.uint8)
                elif isinstance(seq_data, np.ndarray) and seq_data.dtype == np.uint8:
                    # 如果已经是字节数组
                    byte_array = seq_data
                else:
                    # 尝试转换为字节数组
                    byte_array = np.array(seq_data, dtype=np.uint8)
                # 确保序列长度一致
                if len(byte_array) != self.seq_length:
                    raise ValueError(
                        f"Sequence length mismatch: Expected {self.seq_length}, got {len(byte_array)}")
                # 转换为tensor并归一化到[0,1]范围
                seq_tensor = torch.from_numpy(byte_array).float() / 255.0

        except Exception as e:
            raise ValueError(f"Error loading sequence data from {sample['seq_path']}")

        return {
            'image': img,
            'sequence': seq_tensor,
            'label': sample['class_idx'],
        }



if __name__ == '__main__':
    # 测试数据集
    img_path = "../../data/CTU_10_shot/dataset1"
    seq_path = "../../data/CTU_10_shot/SeqBranch/dataset1"
    
    dataset = DualBranchDataset(img_path, seq_path)
    print(f"Dataset size: {len(dataset)}")
    print(f"Classes: {dataset.classes}")
    
    if len(dataset) > 0:
        sample = dataset[0]
        print(f"Sample keys: {sample.keys()}")
        print(f"Image shape: {sample['image'].shape}")
        print(f"Sequence shape: {sample['sequence'].shape}")
        print(f"Attention mask shape: {sample['attention_mask'].shape}")
        print(f"Label: {sample['label']}")
