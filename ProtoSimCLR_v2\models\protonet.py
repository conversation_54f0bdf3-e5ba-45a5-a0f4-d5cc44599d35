import torch
import torch.nn as nn
import torch.nn.functional as F



class ProtoSCL(nn.Module):
    """
    ProtoNet训练实现
    """
    def __init__(self, **kwargs):
        """
        ProtoNet模型初始化
        :param kwargs:
            - encoder: 特征提取器模型
            - temperature: 温度参数 (默认10.0)
            - pretrained_path: 预训练模型路径 (可选)
        """
        super(ProtoSCL, self).__init__()
        self.backbone = kwargs.get('backbone')
        self.n_way = kwargs.get('n_way', 10)  # 类别数
        self.k_shot = kwargs.get('k_shot', 10)
        self.k_query = kwargs.get('k_query', 20)
        self.n_views = kwargs.get('n_views', 2)  # 对比学习视图数
        self.simclr_batch = kwargs.get('simclr_batch', 256)  # 对比学习批次大小
        self.simclr_temperature = nn.Parameter(torch.tensor(0.07), requires_grad=True)
        self.protonet_temperature = nn.Parameter(torch.tensor(10.0), requires_grad=True)
        self.device = kwargs.get('device', 'cpu')
        if self.backbone is None:
            raise ValueError("backbone must be provided in kwargs")
        # 真实的批次大小,我的对比学习任务数据直接从元学习任务中提取
        self.simclr_batch = min(self.simclr_batch, self.n_way * (self.k_shot + self.k_query))

    def get_embedding(self,DualBranchInput):
        """
        获取图像的嵌入特征
        """
        img_branch_input = DualBranchInput['image']
        seq_branch_input = DualBranchInput['sequence']
        outputs = self.backbone(img_branch_input=img_branch_input, seq_branch_input=seq_branch_input)
        out_embedding = outputs['out_embedding']
        return F.normalize(out_embedding, dim=-1)
    def compute_prototypes(self, support_features):
        """
        计算类别原型

        Args:
            support_features: 支持集特征 [n_way * k_shot, dim]
            n_way: 类别数
            k_shot: 每类样本数

        Returns:
            prototypes: 类别原型 [n_way, dim]
        """
        n_way = self.n_way
        k_shot = self.k_shot
        # 重塑为 [n_way, k_shot, dim] 然后计算均值
        prototypes = support_features.reshape(n_way, k_shot, -1).mean(1)
        # prototypes的形状为 [n_way, dim]
        # 归一化原型
        return F.normalize(prototypes, dim=-1)

    def compute_logits(self, query_features, prototypes):
        """
        计算查询样本到原型的相似度logits

        Args:
            query_features: 查询集特征 [n_query, dim]
            prototypes: 类别原型 [n_way, dim]

        Returns:
            logits: 相似度logits [n_query, n_way]
        """
        # 余弦相似度 * 温度参数
        logits = torch.matmul(query_features, prototypes.t()) * self.protonet_temperature
        return logits

    def info_nce_loss(self, features):

        labels = torch.cat([torch.arange(self.simclr_batch) for i in range(self.n_views)], dim=0)
        labels = (labels.unsqueeze(0) == labels.unsqueeze(1)).float()
        labels = labels.to(self.device)

        features = F.normalize(features, dim=1)
        features = features.to(self.device)
        similarity_matrix = torch.matmul(features, features.T)
        # assert similarity_matrix.shape == (
        #     self.args.n_views * self.args.batch_size, self.args.n_views * self.args.batch_size)
        # assert similarity_matrix.shape == labels.shape

        # discard the main diagonal from both: labels and similarities matrix
        mask = torch.eye(labels.shape[0], dtype=torch.bool).to(self.device)
        labels = labels[~mask].view(labels.shape[0], -1)
        similarity_matrix = similarity_matrix[~mask].view(similarity_matrix.shape[0], -1)
        # assert similarity_matrix.shape == labels.shape

        # select and combine multiple positives
        positives = similarity_matrix[labels.bool()].view(labels.shape[0], -1)

        # select only the negatives the negatives
        negatives = similarity_matrix[~labels.bool()].view(similarity_matrix.shape[0], -1)

        logits = torch.cat([positives, negatives], dim=1)
        labels = torch.zeros(logits.shape[0], dtype=torch.long).to(self.device)

        logits = logits / self.simclr_temperature # 使用一个较小的温度参数
        return logits, labels

    def episode_forward(self, support_x, query_x):
        """
        处理一个元学习任务的前向传播
        :param support_x:
        :param query_x:
        :return:
        """
        # 提取特征
        if support_x.dim() != 4 or query_x.dim() != 4:
            support_x = support_x.view(-1, *support_x.shape[-3:])  # [n_way * k_shot, C, H, W]
            query_x = query_x.view(-1, *query_x.shape[-3:])
        support_features = self.get_embedding(support_x)  # [n_way * k_shot, dim]
        query_features = self.get_embedding(query_x)      # [n_way * q_query, dim]
        # 计算原型
        prototypes = self.compute_prototypes(support_features)
        # 计算logits
        logits = self.compute_logits(query_features, prototypes)

        return logits

    def forward(self, inputs):
        '''
        正常的前向传播
        :param x:
        :return:
        '''
        img_branch_input = inputs['image']
        img_branch_input = torch.cat(img_branch_input, dim=0)
        seq_branch_input = inputs['sequence']
        seq_branch_input = torch.cat(seq_branch_input, dim=0)
        outputs = self.backbone(img_branch_input=img_branch_input, seq_branch_input=seq_branch_input)
        return outputs


if __name__ == "__main__":
    # Example usage
    from resnet_simclr import ResNetSimCLR
    n_way = 5
    k_shot = 3
    q_query = 3
    model = ProtoSCL(backbone=ResNetSimCLR(base_model='resnet18', out_dim=128),
                    n_way=n_way, k_shot=k_shot, temperature=10.0)
    support_x = torch.randn(15, 3, 224, 224)  # 5 classes, 3 shots each
    query_x = torch.randn(15, 3, 224, 224)    # 5 classes, 3 queries each


    logits = model(support_x, query_x)
    print(logits.shape)  # Should be [15, 5] for the example above