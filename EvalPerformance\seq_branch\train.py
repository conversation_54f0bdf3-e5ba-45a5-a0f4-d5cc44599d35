import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import datasets, transforms, models
from torch.utils.data import DataLoader
import os
import yaml
import argparse
from tqdm import tqdm  # 添加tqdm进度条支持
from torch.utils.tensorboard import SummaryWriter  # 添加TensorBoard支持
import time
import datetime


def parse_args():
    parser = argparse.ArgumentParser(description='Finetuning with YAML config')
    parser.add_argument('-c','--config', type=str, required=True,
                        help='Path to YAML configuration file')
    return parser.parse_args()


def load_config(config_path):
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


# 解析命令行参数
args = parse_args()

# 加载YAML配置
config = load_config(args.config)

# 设置GPU设备
if 'CUDA_VISIBLE_DEVICES' in config:
    os.environ['CUDA_VISIBLE_DEVICES'] = str(config['CUDA_VISIBLE_DEVICES'])

# 使用配置中的参数
train_data_path = config['train_data_path']
val_data_path = config['val_data_path']
pretrained_model_path = config['pretrained_model_path']
batch_size = config['batch_size']
learning_rate = config['learning_rate']
freeze_train = config['freeze_train']
finetune_epochs = config['finetune_epochs']
freeze_epoch = config['freeze_epoch']
save_dir = config['save_dir']
log_name = config['log_name']
# 创建保存目录
os.makedirs(save_dir, exist_ok=True)

# 设置TensorBoard日志目录
log_dir = os.path.join(save_dir, "logs",datetime.datetime.now().strftime('%Y_%m_%d'),log_name)
os.makedirs(log_dir, exist_ok=True)
writer = SummaryWriter(log_dir=log_dir)  # 创建TensorBoard写入器

# 数据转换
from dataset import SequenceDataset
# 加载数据集
# 我的dataset中已经实现了序列分支的归一化，所以不需要额外的数据转换
train_dataset = SequenceDataset(root_folder=str(train_data_path), transform=None)
val_dataset = SequenceDataset(root_folder=str(val_data_path), transform=None)
num_classes = len(train_dataset.class_idx)

# 创建数据加载器
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=4, pin_memory=True)
val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=4, pin_memory=True)

def validate(model, loader, device, epoch, writer=None):
    model.eval()
    correct = 0
    total = 0
    val_loss = 0.0
    criterion = nn.CrossEntropyLoss()
    passbar = tqdm(loader, desc=f'Validation Epoch {epoch + 1}', leave=False)
    with torch.no_grad():
        for images, labels in passbar:
            images, labels = images.to(device), labels.to(device)
            outputs = model(images)
            loss = criterion(outputs, labels)
            val_loss += loss.item() * images.size(0)
            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()

    val_acc = 100 * correct / total
    val_loss = val_loss / len(loader.dataset)

    # 记录验证结果到TensorBoard
    if writer:
        writer.add_scalar('Loss/val', val_loss, epoch)
        writer.add_scalar('Accuracy/val', val_acc, epoch)

    return val_acc, val_loss


if __name__ == '__main__':
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    from model import SeqBranchCLassifier
    # 使用配置参数初始化模型
    model = SeqBranchCLassifier(num_classes=num_classes, pretrained_path=pretrained_model_path)
    model = model.to(device)
    # 加载预训练权重
    if freeze_train:
        model.freeze_backbone()
        # 初始只优化分类器
        optimizer = optim.Adam(model.classifier.parameters(), lr=learning_rate)
    else:
        # 如果不冻结，优化所有参数
        optimizer = optim.Adam(model.parameters(), lr=learning_rate)

    # 添加学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=len(train_loader), eta_min=0,
                                                           last_epoch=-1)

    criterion = nn.CrossEntropyLoss()
    best_acc = 0.0

    print("Starting training...")
    print(f"TensorBoard logs saved to: {log_dir}")

    # 训练循环
    for epoch in range(finetune_epochs):
        start_time = time.time()

        # 解冻逻辑
        if freeze_train and epoch == freeze_epoch:
            model.unfreeze_backbone()
            # 解冻后需要更新优化器以包含所有参数
            optimizer = optim.Adam(model.parameters(), lr=learning_rate)
            print("Optimizer reset for full model training")

        model.train()
        running_loss = 0.0

        # 使用tqdm封装训练过程
        train_bar = tqdm(train_loader, desc=f'Epoch {epoch + 1}/{finetune_epochs}', leave=False)
        for images, labels in train_bar:
            images, labels = images.to(device), labels.to(device)

            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()

            running_loss += loss.item() * images.size(0)
            # 更新进度条显示当前loss
            train_bar.set_postfix(loss=loss.item())

        # 计算平均训练损失
        epoch_loss = running_loss / len(train_loader.dataset)

        # 记录训练损失到TensorBoard
        writer.add_scalar('Loss/train', epoch_loss, epoch)

        # 验证
        val_acc, val_loss = validate(model, val_loader, device, epoch, writer)

        # 更新学习率
        scheduler.step(val_acc)

        # 记录当前学习率
        current_lr = optimizer.param_groups[0]['lr']
        writer.add_scalar('Learning Rate', current_lr, epoch)

        epoch_time = time.time() - start_time

        print(f'Epoch [{epoch + 1}/{finetune_epochs}] '
              f'Time: {epoch_time:.1f}s '
              f'Loss: {epoch_loss:.4f} '
              f'Val Loss: {val_loss:.4f} '
              f'Val Acc: {val_acc:.2f}% '
              f'LR: {current_lr:.6f}')

        # 保存最佳模型
        if val_acc > best_acc:
            best_acc = val_acc
            print(f"New best model accuracy: {best_acc:.2f}")

    # 关闭TensorBoard写入器
    writer.close()
    print(f"Training complete. Best validation accuracy: {best_acc:.2f}%")
    print(f"TensorBoard logs saved to: {log_dir}")
    print(f"To view results, run: tensorboard --logdir={log_dir}")