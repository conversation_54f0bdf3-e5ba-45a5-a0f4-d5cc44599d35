# 在main函数中添加模型结构可视化
def visualize_model_structure(model, logger, device):
    """
    可视化模型结构
    """
    try:
        from torch.utils.tensorboard import SummaryWriter
        # 创建示例输入
        dummy_img = torch.randn(1, 3, 32, 32).to(device)
        dummy_seq = torch.randn(1, 1024).to(device)  # 改为FloatTensor

        # 记录模型图结构
        logger.writer.add_graph(model.backbone.img_branch_encoder, dummy_img)
        logger.writer.add_graph(model.backbone.seq_branch_encoder, dummy_seq)

        print("Model structure visualization added to TensorBoard")
    except Exception as e:
        print(f"Error occurs, No graph saved")
        print(f"Failed to visualize model structure: {e}")
        # 不抛出异常，继续训练



import torch
# 在train_one_epoch函数中添加样本可视化
def visualize_samples(support_x, query_x, query_y, logger, global_step, max_samples=8):
    """
    可视化部分样本
    """
    try:
        import torchvision.transforms as transforms

        # 反归一化图像以便显示
        def denormalize(tensor):
            mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1).to(tensor.device)
            std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1).to(tensor.device)
            return torch.clamp(tensor * std + mean, 0, 1)

        # 可视化支持集图像
        img_samples = support_x['image'][:max_samples].cpu()  # 移到CPU进行可视化
        denorm_imgs = denormalize(img_samples)
        logger.writer.add_images('Support/Images', denorm_imgs, global_step)

        # 可视化查询集图像
        query_img_samples = query_x['image'][:max_samples].cpu()  # 移到CPU进行可视化
        denorm_query_imgs = denormalize(query_img_samples)
        logger.writer.add_images('Query/Images', denorm_query_imgs, global_step)

        # 记录序列长度信息
        seq_lengths = [len(seq.nonzero()) for seq in support_x['sequence'][:max_samples].cpu()]
        logger.writer.add_text('Support/Sequence_Lengths',
                               str(seq_lengths), global_step)

        print(f"Sample visualization added to TensorBoard at step {global_step}")
    except Exception as e:
        print(f"Failed to visualize samples: {e}")


# 在train_one_epoch或validate_one_epoch中添加嵌入空间可视化
def visualize_embeddings(features, query_y, logger, global_step, max_points=500):
    """
    可视化嵌入空间分布
    """
    try:
        from sklearn.decomposition import PCA
        import numpy as np

        # 获取嵌入特征
        img_embedding = features['img_embedding'].detach().cpu().numpy()
        seq_embedding = features['seq_embedding'].detach().cpu().numpy()
        labels = query_y.detach().cpu().numpy()

        # 限制点数以避免可视化过于拥挤
        if len(labels) > max_points:
            indices = np.random.choice(len(labels), max_points, replace=False)
            img_embedding = img_embedding[indices]
            seq_embedding = seq_embedding[indices]
            labels = labels[indices]

        # 使用PCA降维到3D进行可视化
        pca = PCA(n_components=3)

        # 图像分支嵌入
        img_pca = pca.fit_transform(img_embedding)
        logger.writer.add_embedding(
            img_pca,
            metadata=labels.tolist(),
            tag=f'Embeddings/Image_Branch_Step_{global_step}'
        )

        # 序列分支嵌入
        seq_pca = pca.fit_transform(seq_embedding)
        logger.writer.add_embedding(
            seq_pca,
            metadata=labels.tolist(),
            tag=f'Embeddings/Sequence_Branch_Step_{global_step}'
        )

        # 合并两个分支进行对比可视化
        combined_embedding = np.concatenate([img_embedding, seq_embedding], axis=0)
        combined_labels = np.concatenate([labels, labels], axis=0)
        combined_metadata = [f"{label}_img" if i < len(labels) else f"{label}_seq"
                             for i, label in enumerate(combined_labels)]

        combined_pca = pca.fit_transform(combined_embedding)
        logger.writer.add_embedding(
            combined_pca,
            metadata=combined_metadata,
            tag=f'Embeddings/Combined_Branch_Step_{global_step}'
        )

        print(f"Embedding visualization added to TensorBoard at step {global_step}")
    except Exception as e:
        print(f"Failed to visualize embeddings: {e}")


# 如果需要更高质量的可视化，可以添加t-SNE
def visualize_embeddings_tsne(features, query_y, logger, global_step, max_points=500):
    """
    使用t-SNE进行嵌入空间可视化
    """
    try:
        from sklearn.manifold import TSNE
        import numpy as np

        # 获取嵌入特征
        img_embedding = features['img_embedding'].detach().cpu().numpy()
        seq_embedding = features['seq_embedding'].detach().cpu().numpy()
        labels = query_y.detach().cpu().numpy()

        # 限制点数
        if len(labels) > max_points:
            indices = np.random.choice(len(labels), max_points, replace=False)
            img_embedding = img_embedding[indices]
            seq_embedding = seq_embedding[indices]
            labels = labels[indices]

        # t-SNE降维
        tsne = TSNE(n_components=2, random_state=42)

        # 图像分支
        img_tsne = tsne.fit_transform(img_embedding)
        logger.writer.add_embedding(
            img_tsne,
            metadata=labels.tolist(),
            tag=f'TSNE/Image_Branch_Step_{global_step}'
        )

        # 序列分支
        seq_tsne = tsne.fit_transform(seq_embedding)
        logger.writer.add_embedding(
            seq_tsne,
            metadata=labels.tolist(),
            tag=f'TSNE/Sequence_Branch_Step_{global_step}'
        )

    except ImportError:
        print("t-SNE not available, skipping t-SNE visualization")
    except Exception as e:
        print(f"Failed to visualize embeddings with t-SNE: {e}")
