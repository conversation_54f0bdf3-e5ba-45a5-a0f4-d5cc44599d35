import argparse
import torch
import torch.backends.cudnn as cudnn
import torch.distributed as dist
import torch.multiprocessing as mp
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.utils.data.distributed import DistributedSampler
from torchvision import models
from data_aug.contrastive_learning_dataset import ContrastiveLearningDataset
from models.llama_encoder import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,LlamaEncoderConfig
from models.bert_encoder import <PERSON><PERSON><PERSON><PERSON>,BertEncoderConfig
from simclr import SimCLR

model_names = sorted(name for name in models.__dict__
                     if name.islower() and not name.startswith("__")
                     and callable(models.__dict__[name]))

parser = argparse.ArgumentParser(description='PyTorch SimCLR')
parser.add_argument('-data', metavar='DIR', default='./datasets',
                    help='path to dataset')
parser.add_argument('-dataset-name', default='stl10',
                    help='dataset name', choices=['stl10', 'cifar10','custom','sequence'])
parser.add_argument('-a', '--arch', metavar='ARCH', default='resnet18',
                    choices=model_names,
                    help='model architecture: ' +
                         ' | '.join(model_names) +
                         ' (default: resnet50)')
parser.add_argument('-j', '--workers', default=12, type=int, metavar='N',
                    help='number of data loading workers (default: 32)')
parser.add_argument('--epochs', default=200, type=int, metavar='N',
                    help='number of total epochs to run')
parser.add_argument('-b', '--batch-size', default=256, type=int,
                    metavar='N',
                    help='mini-batch size (default: 256), this is the total '
                         'batch size of all GPUs on the current node when '
                         'using Data Parallel or Distributed Data Parallel')
parser.add_argument('--lr', '--learning-rate', default=0.0003, type=float,
                    metavar='LR', help='initial learning rate', dest='lr')
parser.add_argument('--wd', '--weight-decay', default=1e-4, type=float,
                    metavar='W', help='weight decay (default: 1e-4)',
                    dest='weight_decay')
parser.add_argument('--seed', default=None, type=int,
                    help='seed for initializing training. ')
parser.add_argument('--disable-cuda', action='store_true',
                    help='Disable CUDA')
parser.add_argument('--fp16-precision', action='store_true',
                    help='Whether or not to use 16-bit precision GPU training.')

parser.add_argument('--out_dim', default=128, type=int,
                    help='feature dimension (default: 128)')
parser.add_argument('--log-every-n-steps', default=100, type=int,
                    help='Log every n steps')
parser.add_argument('--temperature', default=0.07, type=float,
                    help='softmax temperature (default: 0.07)')
parser.add_argument('--n-views', default=2, type=int, metavar='N',
                    help='Number of views for contrastive learning training.')
parser.add_argument('--gpu-index', default=0, type=int, help='Gpu index.')
parser.add_argument('--dist-url', default='env://', type=str,
                    help='url used to set up distributed training')
parser.add_argument('--dist-backend', default='nccl', type=str,
                    help='distributed backend')
parser.add_argument('--rank', default=0, type=int,
                    help='node rank for distributed training')
parser.add_argument('--world-size', default=1, type=int,
                    help='number of nodes for distributed training')


def setup(rank, world_size, args):
    # Initialize the process group
    dist.init_process_group(
        backend=args.dist_backend,
        init_method=args.dist_url,
        world_size=world_size,
        rank=rank
    )


def cleanup():
    dist.destroy_process_group()


def main_worker(rank, args):
    args.rank = rank
    args.gpu_index = rank
    
    print(f"Using GPU: {args.gpu_index} for training.")
    
    setup(rank, args.world_size ,args)
    
    # Set device
    torch.cuda.set_device(args.gpu_index)
    args.device = torch.device('cuda', args.gpu_index)
    
    # Fix the seed for reproducibility
    if args.seed is not None:
        torch.manual_seed(args.seed + rank)
        cudnn.deterministic = True
        cudnn.benchmark = False
    
    # Create dataset
    dataset = ContrastiveLearningDataset(args.data)
    train_dataset = dataset.get_dataset(args.dataset_name, args.n_views)
    
    # Create distributed sampler
    train_sampler = DistributedSampler(
        train_dataset,
        num_replicas=args.world_size,
        rank=rank,
        shuffle=True
    )
    
    # Create data loader
    train_loader = torch.utils.data.DataLoader(
        train_dataset, 
        batch_size=args.batch_size,
        shuffle=False,  # sampler handles shuffling
        num_workers=args.workers,
        pin_memory=True,
        drop_last=True,
        sampler=train_sampler
    )
    
    # Create model
    model_cfg = BertEncoderConfig()
    model = BertEncoder(model_cfg)
    model = model.to(args.device)
    
    # Wrap model in DDP
    model = DDP(model, device_ids=[args.gpu_index], find_unused_parameters=True)
    
    # Create optimizer and scheduler
    optimizer = torch.optim.Adam(model.parameters(), args.lr, weight_decay=args.weight_decay)
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
        optimizer, 
        T_max=len(train_loader), 
        eta_min=0,
        last_epoch=-1
    )
    
    # Create SimCLR trainer
    simclr = SimCLR(model=model, optimizer=optimizer, scheduler=scheduler, args=args)
    
    # Train
    simclr.train(train_loader)
    
    # Clean up
    cleanup()


def main():
    args = parser.parse_args()
    assert args.n_views == 2, "Only two view training is supported. Please use --n-views 2."
    
    if args.disable_cuda or not torch.cuda.is_available():
        print("CUDA is disabled or not available. Using CPU.")
        args.device = torch.device('cpu')
        args.gpu_index = -1
        args.world_size = 1
        args.rank = 0
        main_worker(0, args)
    else:
        if args.world_size > 1:
            print("Using distributed training with {} processes.".format(args.world_size))
            mp.spawn(
                main_worker,
                args=(args,),
                nprocs=args.world_size,
                join=True
            )
        else:
            print("Using single GPU training.")
            main_worker(0, args)


if __name__ == "__main__":
    main()