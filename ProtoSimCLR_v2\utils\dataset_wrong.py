from torch.utils.data import Dataset
from torchvision import transforms
import os
from torchvision.datasets import ImageFolder
import torch
import numpy as np
from torch import nn
from PIL import Image
import pickle
from gaussian_blur import GaussianBlur
from view_generator import ContrastiveLearningViewGenerator
class SeqBranchTransform:
    """针对字节流序列数据的增强变换类"""

    def __init__(self, noise_level=0.01, dropout_prob=0.1, scale_range=(0.9, 1.1)):
        self.noise_level = noise_level
        self.dropout_prob = dropout_prob
        self.scale_range = scale_range

    def add_noise(self, sequence):
        """添加均匀噪声"""
        noise = torch.rand_like(sequence) * self.noise_level * 2 - self.noise_level
        noisy_sequence = sequence + noise
        return torch.clamp(noisy_sequence, 0, 1)  # 保持在[0,1]范围内

    def random_dropout(self, sequence):
        """随机将一些字节设为0"""
        if self.dropout_prob > 0:
            mask = torch.rand_like(sequence) > self.dropout_prob
            return sequence * mask.float()
        return sequence

    def random_scale(self, sequence):
        """随机缩放"""
        scale = torch.FloatTensor(1).uniform_(self.scale_range[0], self.scale_range[1])
        scaled_sequence = sequence * scale
        return torch.clamp(scaled_sequence, 0, 1)  # 保持在[0,1]范围内

    def random_offset(self, sequence):
        """随机偏移"""
        offset = torch.FloatTensor(1).uniform_(-0.1, 0.1)
        offset_sequence = sequence + offset
        return torch.clamp(offset_sequence, 0, 1)  # 保持在[0,1]范围内

    def random_subsequence(self, sequence):
        """随机提取子序列并填充到原长度"""
        length = sequence.size(0)
        if length > 256:  # 只有当序列足够长时才进行子序列采样
            sub_len = max(length // 2, 10)
            start_idx = torch.randint(0, length - sub_len + 1, (1,)).item()
            subsequence = sequence[start_idx:start_idx + sub_len]

            # 创建新序列并随机放置子序列
            new_sequence = torch.zeros_like(sequence)
            place_idx = torch.randint(0, length - sub_len + 1, (1,)).item()
            new_sequence[place_idx:place_idx + sub_len] = subsequence
            return new_sequence
        return sequence

    def __call__(self, sequence):
        """应用随机增强变换"""
        # 随机应用各种增强
        transforms = [
            (self.add_noise, 0.5),
            (self.random_dropout, 0.3),
            (self.random_scale, 0.3),
            (self.random_offset, 0.3),
            (self.random_subsequence, 0.2)
        ]

        for transform_func, prob in transforms:
            if torch.rand(1) < prob:
                sequence = transform_func(sequence)

        return sequence

class ImgBranchTransform:
    def __init__(self, size=32, s=1):
        self.size = size
        self.s = s
    def __call__(self, img):
        s = self.s
        size = self.size
        """Return a set of data augmentation transformations as described in the SimCLR paper."""
        color_jitter = transforms.ColorJitter(0.8 * s, 0.8 * s, 0.8 * s, 0.2 * s)
        data_transforms = transforms.Compose([
                                              transforms.RandomHorizontalFlip(),
                                              transforms.RandomApply([color_jitter], p=0.8),
                                              transforms.RandomGrayscale(p=0.2),
                                              GaussianBlur(kernel_size=int(0.1 * size)),
                                              transforms.ToTensor()])
        return data_transforms(img)


class DualBranchDataset(Dataset):
    """双分支数据集：同时加载图像和序列数据"""

    def __init__(self, **kwargs):
        """
        :param img_root: 图像数据根目录
        :param seq_root: 序列数据根目录
        :param transform: 图像变换
        :param seq_length: 序列长度
        """
        self.img_root = kwargs.get('img_root', None)
        self.seq_root = kwargs.get('seq_root', None)
        self.img_branch_transform = kwargs.get('img_branch_transform', None)
        self.seq_branch_transform = kwargs.get('seq_branch_transform', None)
        self.img_size = kwargs.get('img_size', 32)
        self.seq_length = kwargs.get('seq_length', 1024)

        # 获取类别列表
        self.classes = sorted([d for d in os.listdir(self.img_root)])
        self.class_to_idx = {cls_name: idx for idx, cls_name in enumerate(self.classes)}

        # 构建样本列表
        self.samples = []
        self._build_samples()

    def _build_samples(self):
        """构建样本列表，确保图像和序列数据一一对应"""
        # 遍历文件夹中的所有PKL文件
        # 基于seq路径来得到图片路径
        for class_name in os.listdir(self.seq_root):
            class_dir = os.path.join(self.seq_root, class_name)
            if os.path.isdir(class_dir):
                # 递归遍历class_dir下所有文件
                for root, _, files in os.walk(class_dir):
                    for filename in files:
                        if filename.endswith('.pkl'):
                            seq_path = os.path.join(root, filename)
                            rel_path = os.path.relpath(seq_path,self.seq_root)  # 获取seq_path相对于seq_root的相对路径（如"class1/subdir/file.pkl"）
                            img_rel_path = os.path.splitext(rel_path)[0] + '.png'  # 替换扩展名为.png（如"class1/subdir/file.png"）
                            img_path = os.path.join(self.img_root, img_rel_path)  # 拼接为完整图像路径
                            if not os.path.exists(img_path):
                                raise FileNotFoundError(f"Image file not found: {img_path}")
                            label = self.class_to_idx[class_name]
                            self.samples.append({
                                'img_path': img_path,
                                'seq_path': seq_path,
                                'class_idx': label,
                            })

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        sample = self.samples[idx]
        try:
            # 加载图像
            img = Image.open(sample['img_path']).convert('RGB')
            if self.img_branch_transform:
                img = self.img_branch_transform(img)
        except Exception as e:
            raise ValueError(f"Error loading image from {sample['img_path']}")

        # 加载序列数据
        try:
            with open(sample['seq_path'], 'rb') as f:
                seq_data = pickle.load(f)

                # 处理字节流数据
                if isinstance(seq_data, bytes):
                    # 将字节转换为数值数组
                    byte_array = np.frombuffer(seq_data, dtype=np.uint8)
                elif isinstance(seq_data, np.ndarray) and seq_data.dtype == np.uint8:
                    # 如果已经是字节数组
                    byte_array = seq_data
                else:
                    # 尝试转换为字节数组
                    byte_array = np.array(seq_data, dtype=np.uint8)
                # 确保序列长度一致
                if len(byte_array) != self.seq_length:
                    raise ValueError(
                        f"Sequence length mismatch: Expected {self.seq_length}, got {len(byte_array)}")
                # 转换为tensor并归一化到[0,1]范围
                seq_tensor = torch.from_numpy(byte_array).float() / 255.0
                if self.seq_branch_transform:
                    seq_tensor = self.seq_branch_transform(seq_tensor)

        except Exception as e:
            raise ValueError(f"Error loading sequence data from {sample['seq_path']}")

        return {
            'image': img,
            'sequence': seq_tensor,
            'label': sample['class_idx'],
        }

class ProtoSCLDataset(Dataset):
    """
    Few-shot学习数据集类，继承自torch.utils.data.Dataset
    """
    def __init__(self, **kwargs):
        self.img_root = kwargs.get('img_root', '')
        self.seq_root = kwargs.get('seq_root', '')
        self.n_views = kwargs.get('n_views', 2)  # 对比学习视图数量
        self.img_transform = kwargs.get('img_transform', ContrastiveLearningViewGenerator(
            base_transform=ImgBranchTransform(size=32),
            n_views=self.n_views,
        ))
        self.seq_transform = kwargs.get('seq_transform', ContrastiveLearningViewGenerator(
            base_transform=SeqBranchTransform(),
            n_views=self.n_views,
        ))
        if self.img_root and self.seq_root:
            self._check_directory_consistency()
        else:
            raise ValueError("Both img_root and seq_root must be provided")

        self.n_way = kwargs.get('n_way', 5)
        self.k_shot = kwargs.get('k_shot', 10)
        self.k_query = kwargs.get('k_query', 20)  # 修正参数名
        self.contrastive_learning_batch = kwargs.get('contrastive_learning_batch', 256)
        self.episodes_num = kwargs.get('episodes_num', 100)  # 每个epoch的episode数量
        self.dataset = DualBranchDataset(img_root=self.img_root,
                                         seq_root=self.seq_root,
                                         img_branch_transform=self.img_transform,
                                         seq_branch_transform=self.seq_transform,
                                         img_size=32,
                                         seq_length=1024)
        self.split_classes = {}
        # 按类别划分数据
        for idx in range(len(self.dataset)):
            label = self.dataset[idx]['label']
            if label not in self.split_classes:
                self.split_classes[label] = []
            self.split_classes[label].append(idx)

    def __len__(self):
        return self.episodes_num
    def _check_directory_consistency(self):
        """检查图像和序列数据根目录下的子目录结构是否一致"""
        # 获取img_root下的所有子目录
        img_classes = set()
        for item in os.listdir(self.img_root):
            item_path = os.path.join(self.img_root, item)
            if os.path.isdir(item_path):
                img_classes.add(item)

        # 获取seq_root下的所有子目录
        seq_classes = set()
        for item in os.listdir(self.seq_root):
            item_path = os.path.join(self.seq_root, item)
            if os.path.isdir(item_path):
                seq_classes.add(item)

        # 检查两个目录结构是否一致
        if img_classes != seq_classes:
            missing_in_img = seq_classes - img_classes
            missing_in_seq = img_classes - seq_classes
            error_msg = "Directory structure inconsistency between img_root and seq_root:\n"
            if missing_in_img:
                error_msg += f"Classes in seq_root but not in img_root: {missing_in_img}\n"
            if missing_in_seq:
                error_msg += f"Classes in img_root but not in seq_root: {missing_in_seq}\n"

            raise ValueError(error_msg)
    def __getitem__(self,idx):
        """
        获取一个episode的数据
        :param idx: episode索引
        :return: 支持集和查询集图像及其标签
        """
        import random
        import PIL
        # 随机选择n_way个类别
        selected_classes = random.sample(list(self.split_classes.keys()), self.n_way)

        # 记录对比学习数据的个数
        support_images = [[] for _ in range(self.n_views)]  # 按视图分组存储支持集图像
        support_sequences = [[] for _ in range(self.n_views)]  # 按视图分组存储支持集序列
        support_label = []  # 支持集标签
        query_images = [[] for _ in range(self.n_views)]  # 按视图分组存储查询集图像
        query_sequences = [[] for _ in range(self.n_views)]  # 按视图分组存储查询集序列
        query_label = []  # 查询集标签

        for episodes_class,class_idx in enumerate(selected_classes):
            # selected_classes存储的是图像数据和类别数据对应原始dataset的索引
            items_id = self.split_classes[class_idx]
            if len(items_id) < self.k_shot + self.k_query:
                # 数量不足时进行重采样（有放回采样）,会导致支持集和查询集样本重复
                raise ValueError(
                    f"Insufficient number of samples for class {class_idx}. "
                    f"Expected at least {self.k_shot + self.k_query} samples, got {len(items_id)}")
            samples = random.sample(items_id, self.k_shot + self.k_query)
            # 分割支持集和查询集
            support_samples = samples[:self.k_shot]
            query_samples = samples[self.k_shot:]
            # 添加支持集样本
            for item_id in support_samples:
                item = self.dataset[item_id]
                img = item['image']
                seq = item['sequence']
                if not isinstance(img, list):
                    raise ValueError("Expected a list of images for support samples")
                if not isinstance(seq, list):
                    raise ValueError("Expected a list of sequences for support samples")
                for view_idx in range(self.n_views):
                    support_images[view_idx].append(item['image'][view_idx])
                    support_sequences[view_idx].append(item['sequence'][view_idx])

                support_label.append(episodes_class)

            # 添加查询集样本
            for item_id in query_samples:
                item = self.dataset[item_id]
                img = item['image']
                seq = item['sequence']
                if not isinstance(img, list):
                    raise ValueError("Expected a list of images for support samples")
                if not isinstance(seq, list):
                    raise ValueError("Expected a list of sequences for support samples")
                for view_idx in range(self.n_views):
                    query_images[view_idx].append(item['image'][view_idx])
                    query_sequences[view_idx].append(item['sequence'][view_idx])
                # 添加标签（每个样本的n_views个视图共享同一个标签）
                query_label.append(episodes_class)

        # 转换为Tensor
        support_item = {
            # 修改这里：对每个视图，将该视图下的所有样本堆叠
            'image': [torch.stack(support_images[view_idx]) for view_idx in range(self.n_views)],
            'sequence': [torch.stack(support_sequences[view_idx]) for view_idx in range(self.n_views)],
        }
        support_label = torch.tensor(support_label, dtype=torch.long)
        query_item = {
            'image': [torch.stack(query_images[view_idx]) for view_idx in range(self.n_views)],
            'sequence': [torch.stack(query_sequences[view_idx]) for view_idx in range(self.n_views)]
        }
        query_label = torch.tensor(query_label, dtype=torch.long)
        episode = {
            'support_item': support_item,
            'support_label': support_label,
            'query_item': query_item,
            'query_label': query_label
        }

        return episode


if __name__ == "__main__":
    import matplotlib.pyplot as plt
    import os
    import numpy as np
    import torch

    # 创建调试目录
    os.makedirs('./debug_cat_images', exist_ok=True)

    img_path = '../temp/cifar10_img'
    seq_path = '../temp/cifar10_seq'
    dataset = ProtoSCLDataset(img_root=img_path, seq_root=seq_path, n_way=3, k_shot=1, k_query=1)

    # 打印参数确认
    print(f"n_way={dataset.n_way}, k_shot={dataset.k_shot}, k_query={dataset.k_query}, n_views={dataset.n_views}")
    # 自定义collate函数
    def collate_fn(batch):
        return batch[0]  # 直接返回单个episode
    # 创建数据加载器
    dataloader = torch.utils.data.DataLoader(
        dataset,
        batch_size=1,
        shuffle=True,
        collate_fn=collate_fn
    )

    # 获取一个episode
    for episode in dataloader:
        # 获取原始数据
        support_item = episode['support_item']
        img = support_item['image']
        print(len(img))

        break  # 只处理一个episode