CUDA_VISIBLE_DEVICES: '2'
# 数据相关配置
data:
  img_root: '../data/GEN_DATAS/'
  seq_root: '../data/PKL_GEN_DATAS/'
  val_img_root: '../data/CTU_Processed/CTU_IMG/Test'
  val_seq_root: '../data/CTU_Processed/CTU_SEQUENCE/Test'

# SimCLR预训练模型配置
backbone:
  img_pretrained_path: '../EvalPerformance/img_branch/ckpt/resnet18.pth.tar'
  seq_pretrained_path: '../EvalPerformance/seq_branch/ckpt/bert_500epoch.pth.tar'


# ProtoNet配置 - 多分类设置
ProtoSCL:
  n_way: 5              # 5-way分类（根据实际类别数调整）
  k_shot: 10            # 3-shot学习
  k_query: 20           # 查询集样本数
  simclr_batch: 64     #
  episodes_num: 100      # dataset中每个epoch的episode数量
  n_views: 2  # 每个样本的视图数
  device: "cuda"

# 训练配置
training:
  epochs: 5000
  num_workers: 16
  device: "cuda"
  seed: 42
  early_stopping_patience: 20
  gradient_clip_norm: 1.0

# 优化器配置
optimizer:
  type: "adam"
  lr: 0.001  # 微调训练时的学习率
  momentum: 0.9  # SGD优化器的动量
  weight_decay: 1e-4  # 权重衰减
  lr_scheduler:
    type: "cosine"
    T_max: 120
    eta_min: 0.000001

# 保存和日志配置
logging:
  save_dir: "./checkpoints/2_stage_PSCL"
  log_dir: "./logs"
  save_freq: 100
  log_freq: 100
  experiment_name: "ProtoSCL_experiment"

# 评估配置
evaluation:
  eval_freq: 20
  test_episodes: 100
  metrics: ["accuracy", "precision", "recall", "f1_score"]


